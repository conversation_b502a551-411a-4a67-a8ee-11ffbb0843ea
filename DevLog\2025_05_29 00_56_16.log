2025-05-29 00:56:16.5841 [INFO] [StorageComprehensiveTest]::StartComprehensiveTest(152) - === 开始Storage模块综合测试 ===
2025-05-29 00:56:16.6042 [INFO] [StorageComprehensiveTest]::InitializeTestData(200) - 测试数据初始化完成:
2025-05-29 00:56:16.6042 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(595) -   IntValue: 42
2025-05-29 00:56:16.6042 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(596) -   FloatValue: 3.14159
2025-05-29 00:56:16.6042 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(597) -   StringValue: Hello Storage!
2025-05-29 00:56:16.6042 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(598) -   BoolValue: True
2025-05-29 00:56:16.6042 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(599) -   Vector3Value: (1.50, 2.50, 3.50)
2025-05-29 00:56:16.6042 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(600) -   QuaternionValue: (0.65328, -0.27060, 0.65328, 0.27060)
2025-05-29 00:56:16.6042 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(601) -   ColorValue: RGBA(1.000, 0.000, 0.000, 1.000)
2025-05-29 00:56:16.6042 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(602) -   IntArray: [1, 2, 3, 4, 5]
2025-05-29 00:56:16.6042 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(603) -   StringList: [Item1, Item2, Item3]
2025-05-29 00:56:16.6042 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(604) -   StringIntDict: 3 items
2025-05-29 00:56:16.6042 [INFO] [Storage]::InitializeCore(68) - Storage core initialized successfully
2025-05-29 00:56:16.6042 [INFO] [StorageSettings]::InitializePathCache(71) - Storage paths cached successfully
2025-05-29 00:56:16.6042 [INFO] [StorageManager]::Initialize(88) - StorageManager initialized successfully
2025-05-29 00:56:16.6042 [INFO] [StorageComprehensiveTest]::InitializeStorageManager(213) - StorageManager初始化完成
2025-05-29 00:56:16.6042 [INFO] [StorageComprehensiveTest]::CreateTestInstances(226) - 创建测试存储实例...
2025-05-29 00:56:16.6207 [INFO] [StorageTypeMgr]::Initialize(71) - StorageTypeMgr initialized with 36 registered types
2025-05-29 00:56:16.6207 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: PlayerPrefsEncrypted
2025-05-29 00:56:16.6207 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: PlayerPrefsPlain
2025-05-29 00:56:16.6207 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: FileEncrypted
2025-05-29 00:56:16.6207 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: FilePlain
2025-05-29 00:56:16.6207 [INFO] [StorageComprehensiveTest]::CreateTestInstances(264) - 所有测试实例创建完成
2025-05-29 00:56:16.6207 [INFO] [StorageComprehensiveTest]::RunAllTests(272) - 开始执行测试用例...
2025-05-29 00:56:16.6207 [INFO] [StorageComprehensiveTest]::TestStorageInstance(300) - --- 开始测试: PlayerPrefs + AES加密 ---
2025-05-29 00:56:16.6207 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: IntValue, Type: Int32, Value: 42
2025-05-29 00:56:16.6207 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: FloatValue, Type: Single, Value: 3.14159
2025-05-29 00:56:16.6307 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: StringValue, Type: String, Value: Hello Storage!
2025-05-29 00:56:16.6307 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: BoolValue, Type: Boolean, Value: True
2025-05-29 00:56:16.6307 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: Vector3Value, Type: Vector3, Value: (1.50, 2.50, 3.50)
2025-05-29 00:56:16.6307 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: QuaternionValue, Type: Quaternion, Value: (0.65328, -0.27060, 0.65328, 0.27060)
2025-05-29 00:56:16.6307 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: ColorValue, Type: Color, Value: RGBA(1.000, 0.000, 0.000, 1.000)
2025-05-29 00:56:16.6307 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: IntArray, Type: Int32[], Value: [1, 2, 3, 4, 5]
2025-05-29 00:56:16.6307 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: StringList, Type: List`1, Value: ["Item1", "Item2", "Item3"]
2025-05-29 00:56:16.6307 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: StringIntDict, Type: Dictionary`2, Value: {"Score": 1000, "Level": 5, "Lives": 3}
2025-05-29 00:56:16.6307 [INFO] [StorageComprehensiveTest]::SaveTestData(377) - 测试数据已保存到实例: PlayerPrefsEncrypted
2025-05-29 00:56:16.6466 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(119) - Generated inline metadata for 10 items
2025-05-29 00:56:16.6466 [INFO] [StorageInstance]::SaveToFileSync(386) - Instance [PlayerPrefsEncrypted] - Starting sync save operation
2025-05-29 00:56:16.7057 [INFO] [Storage]::WriteToFileWithBackup(242) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\StorageTest_PlayerPrefs_Encrypted
2025-05-29 00:56:16.7057 [INFO] [StorageInstance]::SaveToFileSync(408) - Instance [PlayerPrefsEncrypted] - Data saved synchronously
2025-05-29 00:56:16.7057 [INFO] [StorageComprehensiveTest]::TestStorageInstance(311) - PlayerPrefs + AES加密: 数据保存成功
2025-05-29 00:56:16.7072 [INFO] [StorageCache]::Clear(182) - Cleared 10 items from cache
2025-05-29 00:56:16.7072 [INFO] [StorageInstance]::Clear(227) - Instance [PlayerPrefsEncrypted] - All data cleared from cache
2025-05-29 00:56:16.7072 [INFO] [StorageComprehensiveTest]::TestStorageInstance(321) - PlayerPrefs + AES加密: 内存缓存已清空
2025-05-29 00:56:16.7072 [INFO] [StorageInstance]::LoadFromFileSync(441) - Instance [PlayerPrefsEncrypted] - Starting sync load operation
2025-05-29 01:03:00.3122 [ERROR] [StorageTypeMgr]::CreateWrapperForType(262) - Failed to create wrapper for type 'Int32': Exception has been thrown by the target of an invocation.
2025-05-29 01:03:00.3122 [WARN] [StorageTypeMetadata]::LoadWithInlineMetadata(150) - Failed to convert inline value for key 'IntValue'
2025-05-29 01:03:58.1422 [ERROR] [StorageTypeMgr]::CreateWrapperForType(262) - Failed to create wrapper for type 'Single': Exception has been thrown by the target of an invocation.
2025-05-29 01:03:58.9791 [WARN] [StorageTypeMetadata]::LoadWithInlineMetadata(150) - Failed to convert inline value for key 'FloatValue'
2025-05-29 01:04:01.5683 [ERROR] [StorageTypeMgr]::CreateWrapperForType(262) - Failed to create wrapper for type 'String': Exception has been thrown by the target of an invocation.
2025-05-29 01:04:01.5683 [WARN] [StorageTypeMetadata]::LoadWithInlineMetadata(150) - Failed to convert inline value for key 'StringValue'
2025-05-29 01:04:01.8228 [ERROR] [StorageTypeMgr]::CreateWrapperForType(262) - Failed to create wrapper for type 'Boolean': Exception has been thrown by the target of an invocation.
2025-05-29 01:04:01.8228 [WARN] [StorageTypeMetadata]::LoadWithInlineMetadata(150) - Failed to convert inline value for key 'BoolValue'
