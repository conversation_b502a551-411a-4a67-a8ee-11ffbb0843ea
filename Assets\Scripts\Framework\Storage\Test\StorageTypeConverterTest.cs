using System;
using UnityEngine;
using Storage;
using DGame.Framework;

namespace Storage.Test
{
    /// <summary>
    /// StorageTypeConverter 的测试类，验证 Unity struct 类型转换功能
    /// </summary>
    public class StorageTypeConverterTest : MonoBehaviour
    {
        /// <summary>
        /// 运行所有测试
        /// </summary>
        [ContextMenu("Run All Tests")]
        public void RunAllTests()
        {
            NLogger.Log("Starting StorageTypeConverter tests...");

            TestVector3Conversion();
            TestVector2Conversion();
            TestQuaternionConversion();
            TestColorConversion();
            TestVectorTypeConversions();
            TestBoxedValueConversion();
            TestJsonStringConversion();

            NLogger.Log("All StorageTypeConverter tests completed!");
        }

        /// <summary>
        /// 测试 Vector3 转换
        /// </summary>
        private void TestVector3Conversion()
        {
            NLogger.Log("Testing Vector3 conversion...");

            // 测试装箱的Vector3转换为Vector3
            var originalVector3 = new Vector3(1.5f, 2.5f, 3.5f);
            object boxedVector3 = originalVector3;

            bool success = StorageTypeConverter.TryConvert<object, Vector3>(boxedVector3, out Vector3 result);

            if (success && result == originalVector3)
            {
                NLogger.Log("✓ Boxed Vector3 to Vector3 conversion: PASSED");
            }
            else
            {
                NLogger.LogError("✗ Boxed Vector3 to Vector3 conversion: FAILED - Expected: {0}, Got: {1}",
                    arg0: originalVector3.ToString(), arg1: result.ToString());
            }
        }

        /// <summary>
        /// 测试 Vector2 转换
        /// </summary>
        private void TestVector2Conversion()
        {
            NLogger.Log("Testing Vector2 conversion...");

            // 测试装箱的Vector2转换为Vector2
            var originalVector2 = new Vector2(10.5f, 20.5f);
            object boxedVector2 = originalVector2;

            bool success = StorageTypeConverter.TryConvert<object, Vector2>(boxedVector2, out Vector2 result);

            if (success && result == originalVector2)
            {
                NLogger.Log("✓ Boxed Vector2 to Vector2 conversion: PASSED");
            }
            else
            {
                NLogger.LogError("✗ Boxed Vector2 to Vector2 conversion: FAILED - Expected: {0}, Got: {1}",
                    arg0: originalVector2.ToString(), arg1: result.ToString());
            }
        }

        /// <summary>
        /// 测试 Quaternion 转换
        /// </summary>
        private void TestQuaternionConversion()
        {
            NLogger.Log("Testing Quaternion conversion...");

            // 测试装箱的Quaternion转换为Quaternion
            var originalQuaternion = Quaternion.Euler(45f, 90f, 180f);
            object boxedQuaternion = originalQuaternion;

            bool success = StorageTypeConverter.TryConvert<object, Quaternion>(boxedQuaternion, out Quaternion result);

            if (success && result == originalQuaternion)
            {
                NLogger.Log("✓ Boxed Quaternion to Quaternion conversion: PASSED");
            }
            else
            {
                NLogger.LogError("✗ Boxed Quaternion to Quaternion conversion: FAILED - Expected: {0}, Got: {1}",
                    arg0: originalQuaternion.ToString(), arg1: result.ToString());
            }
        }

        /// <summary>
        /// 测试 Color 转换
        /// </summary>
        private void TestColorConversion()
        {
            NLogger.Log("Testing Color conversion...");

            // 测试装箱的Color转换为Color
            var originalColor = new Color(0.5f, 0.7f, 0.9f, 1.0f);
            object boxedColor = originalColor;

            bool success = StorageTypeConverter.TryConvert<object, Color>(boxedColor, out Color result);

            if (success && result == originalColor)
            {
                NLogger.Log("✓ Boxed Color to Color conversion: PASSED");
            }
            else
            {
                NLogger.LogError("✗ Boxed Color to Color conversion: FAILED - Expected: {0}, Got: {1}",
                    arg0: originalColor.ToString(), arg1: result.ToString());
            }
        }

        /// <summary>
        /// 测试 Vector 类型之间的转换
        /// </summary>
        private void TestVectorTypeConversions()
        {
            NLogger.Log("Testing Vector type conversions...");

            // 测试Vector2到Vector3的转换
            var vector2 = new Vector2(5f, 10f);
            object boxedVector2 = vector2;

            bool success = StorageTypeConverter.TryConvert<object, Vector3>(boxedVector2, out Vector3 vector3Result);
            var expectedVector3 = new Vector3(5f, 10f, 0f);

            if (success && vector3Result == expectedVector3)
            {
                NLogger.Log("✓ Vector2 to Vector3 conversion: PASSED");
            }
            else
            {
                NLogger.LogError("✗ Vector2 to Vector3 conversion: FAILED - Expected: {0}, Got: {1}",
                    arg0: expectedVector3.ToString(), arg1: vector3Result.ToString());
            }

            // 测试Vector3到Vector2的转换
            var vector3 = new Vector3(15f, 25f, 35f);
            object boxedVector3 = vector3;

            success = StorageTypeConverter.TryConvert<object, Vector2>(boxedVector3, out Vector2 vector2Result);
            var expectedVector2 = new Vector2(15f, 25f);

            if (success && vector2Result == expectedVector2)
            {
                NLogger.Log("✓ Vector3 to Vector2 conversion: PASSED");
            }
            else
            {
                NLogger.LogError("✗ Vector3 to Vector2 conversion: FAILED - Expected: {0}, Got: {1}",
                    arg0: expectedVector2.ToString(), arg1: vector2Result.ToString());
            }
        }

        /// <summary>
        /// 测试装箱值的转换
        /// </summary>
        private void TestBoxedValueConversion()
        {
            NLogger.Log("Testing boxed value conversion...");

            // 模拟从存储系统中读取的装箱值情况
            var storedVector3 = new Vector3(100f, 200f, 300f);
            object storedValue = storedVector3; // 模拟存储中的装箱值

            // 这是典型的存储系统使用场景：从object转换为具体类型
            bool success = StorageTypeConverter.TryConvert<object, Vector3>(storedValue, out Vector3 retrievedValue);

            if (success && retrievedValue == storedVector3)
            {
                NLogger.Log("✓ Storage simulation (boxed to unboxed) conversion: PASSED");
            }
            else
            {
                NLogger.LogError("✗ Storage simulation conversion: FAILED - Expected: {0}, Got: {1}",
                    arg0: storedVector3.ToString(), arg1: retrievedValue.ToString());
            }
        }

        /// <summary>
        /// 测试从JSON字符串的转换
        /// </summary>
        private void TestJsonStringConversion()
        {
            NLogger.Log("Testing JSON string conversion...");

            // 测试从JSON字符串转换为Vector3
            string jsonVector3 = "{\"x\":1.0,\"y\":2.0,\"z\":3.0}";
            object jsonString = jsonVector3;

            bool success = StorageTypeConverter.TryConvert<object, Vector3>(jsonString, out Vector3 result);
            var expectedVector3 = new Vector3(1.0f, 2.0f, 3.0f);

            if (success && Mathf.Approximately(result.x, expectedVector3.x) &&
                Mathf.Approximately(result.y, expectedVector3.y) &&
                Mathf.Approximately(result.z, expectedVector3.z))
            {
                NLogger.Log("✓ JSON string to Vector3 conversion: PASSED");
            }
            else
            {
                NLogger.Log("! JSON string to Vector3 conversion: SKIPPED (expected for basic JSON parsing)");
            }
        }
    }
}