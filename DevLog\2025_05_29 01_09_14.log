2025-05-29 01:09:14.5499 [INFO] [StorageComprehensiveTest]::StartComprehensiveTest(152) - === 开始Storage模块综合测试 ===
2025-05-29 01:09:14.5681 [INFO] [StorageComprehensiveTest]::InitializeTestData(200) - 测试数据初始化完成:
2025-05-29 01:09:14.5681 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(595) -   IntValue: 42
2025-05-29 01:09:14.5681 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(596) -   FloatValue: 3.14159
2025-05-29 01:09:14.5681 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(597) -   StringValue: Hello Storage!
2025-05-29 01:09:14.5681 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(598) -   BoolValue: True
2025-05-29 01:09:14.5681 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(599) -   Vector3Value: (1.50, 2.50, 3.50)
2025-05-29 01:09:14.5681 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(600) -   QuaternionValue: (0.65328, -0.27060, 0.65328, 0.27060)
2025-05-29 01:09:14.5681 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(601) -   ColorValue: RGBA(1.000, 0.000, 0.000, 1.000)
2025-05-29 01:09:14.5681 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(602) -   IntArray: [1, 2, 3, 4, 5]
2025-05-29 01:09:14.5681 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(603) -   StringList: [Item1, Item2, Item3]
2025-05-29 01:09:14.5681 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(604) -   StringIntDict: 3 items
2025-05-29 01:09:14.5681 [INFO] [Storage]::InitializeCore(68) - Storage core initialized successfully
2025-05-29 01:09:14.5681 [INFO] [StorageSettings]::InitializePathCache(71) - Storage paths cached successfully
2025-05-29 01:09:14.5681 [INFO] [StorageManager]::Initialize(88) - StorageManager initialized successfully
2025-05-29 01:09:14.5681 [INFO] [StorageComprehensiveTest]::InitializeStorageManager(213) - StorageManager初始化完成
2025-05-29 01:09:14.5681 [INFO] [StorageComprehensiveTest]::CreateTestInstances(226) - 创建测试存储实例...
2025-05-29 01:09:14.5681 [INFO] [StorageTypeMgr]::Initialize(71) - StorageTypeMgr initialized with 36 registered types
2025-05-29 01:09:14.5681 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: PlayerPrefsEncrypted
2025-05-29 01:09:14.5681 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: PlayerPrefsPlain
2025-05-29 01:09:14.5681 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: FileEncrypted
2025-05-29 01:09:14.5681 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: FilePlain
2025-05-29 01:09:14.5681 [INFO] [StorageComprehensiveTest]::CreateTestInstances(264) - 所有测试实例创建完成
2025-05-29 01:09:14.5681 [INFO] [StorageComprehensiveTest]::RunAllTests(272) - 开始执行测试用例...
2025-05-29 01:09:14.5681 [INFO] [StorageComprehensiveTest]::TestStorageInstance(300) - --- 开始测试: PlayerPrefs + AES加密 ---
2025-05-29 01:09:14.5681 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: IntValue, Type: Int32, Value: 42
2025-05-29 01:09:14.5681 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: FloatValue, Type: Single, Value: 3.14159
2025-05-29 01:09:14.5681 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: StringValue, Type: String, Value: Hello Storage!
2025-05-29 01:09:14.5817 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: BoolValue, Type: Boolean, Value: True
2025-05-29 01:09:14.5817 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: Vector3Value, Type: Vector3, Value: (1.50, 2.50, 3.50)
2025-05-29 01:09:14.5817 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: QuaternionValue, Type: Quaternion, Value: (0.65328, -0.27060, 0.65328, 0.27060)
2025-05-29 01:09:14.5817 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: ColorValue, Type: Color, Value: RGBA(1.000, 0.000, 0.000, 1.000)
2025-05-29 01:09:14.5817 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: IntArray, Type: Int32[], Value: [1, 2, 3, 4, 5]
2025-05-29 01:09:14.5817 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: StringList, Type: List`1, Value: ["Item1", "Item2", "Item3"]
2025-05-29 01:09:14.5817 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: StringIntDict, Type: Dictionary`2, Value: {"Score": 1000, "Level": 5, "Lives": 3}
2025-05-29 01:09:14.5817 [INFO] [StorageComprehensiveTest]::SaveTestData(377) - 测试数据已保存到实例: PlayerPrefsEncrypted
2025-05-29 01:09:14.5817 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(119) - Generated inline metadata for 10 items
2025-05-29 01:09:14.5817 [INFO] [StorageInstance]::SaveToFileSync(386) - Instance [PlayerPrefsEncrypted] - Starting sync save operation
2025-05-29 01:09:14.6376 [INFO] [Storage]::WriteToFileWithBackup(242) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\StorageTest_PlayerPrefs_Encrypted
2025-05-29 01:09:14.6376 [INFO] [StorageInstance]::SaveToFileSync(408) - Instance [PlayerPrefsEncrypted] - Data saved synchronously
2025-05-29 01:09:14.6376 [INFO] [StorageComprehensiveTest]::TestStorageInstance(311) - PlayerPrefs + AES加密: 数据保存成功
2025-05-29 01:09:14.6376 [INFO] [StorageCache]::Clear(182) - Cleared 10 items from cache
2025-05-29 01:09:14.6376 [INFO] [StorageInstance]::Clear(227) - Instance [PlayerPrefsEncrypted] - All data cleared from cache
2025-05-29 01:09:14.6376 [INFO] [StorageComprehensiveTest]::TestStorageInstance(321) - PlayerPrefs + AES加密: 内存缓存已清空
2025-05-29 01:09:14.6376 [INFO] [StorageInstance]::LoadFromFileSync(441) - Instance [PlayerPrefsEncrypted] - Starting sync load operation
2025-05-29 01:09:14.6623 [ERROR] [StorageTypeMgr]::CreateWrapperForType(262) - Failed to create wrapper for type 'Int32': Exception has been thrown by the target of an invocation.
2025-05-29 01:09:14.6623 [WARN] [StorageTypeMetadata]::LoadWithInlineMetadata(150) - Failed to convert inline value for key 'IntValue'
2025-05-29 01:09:14.6623 [ERROR] [StorageTypeMgr]::CreateWrapperForType(262) - Failed to create wrapper for type 'Single': Exception has been thrown by the target of an invocation.
2025-05-29 01:09:14.6623 [WARN] [StorageTypeMetadata]::LoadWithInlineMetadata(150) - Failed to convert inline value for key 'FloatValue'
2025-05-29 01:09:14.6623 [ERROR] [StorageTypeMgr]::CreateWrapperForType(262) - Failed to create wrapper for type 'String': Exception has been thrown by the target of an invocation.
2025-05-29 01:09:14.6623 [WARN] [StorageTypeMetadata]::LoadWithInlineMetadata(150) - Failed to convert inline value for key 'StringValue'
2025-05-29 01:09:14.6764 [ERROR] [StorageTypeMgr]::CreateWrapperForType(262) - Failed to create wrapper for type 'Boolean': Exception has been thrown by the target of an invocation.
2025-05-29 01:09:14.6764 [WARN] [StorageTypeMetadata]::LoadWithInlineMetadata(150) - Failed to convert inline value for key 'BoolValue'
2025-05-29 01:09:14.6764 [ERROR] [StorageTypeMgr]::CreateWrapperForType(262) - Failed to create wrapper for type 'Vector3': Exception has been thrown by the target of an invocation.
2025-05-29 01:09:14.6764 [WARN] [StorageTypeMetadata]::LoadWithInlineMetadata(150) - Failed to convert inline value for key 'Vector3Value'
2025-05-29 01:09:14.6764 [ERROR] [StorageTypeMgr]::CreateWrapperForType(262) - Failed to create wrapper for type 'Quaternion': Exception has been thrown by the target of an invocation.
2025-05-29 01:09:14.6764 [WARN] [StorageTypeMetadata]::LoadWithInlineMetadata(150) - Failed to convert inline value for key 'QuaternionValue'
2025-05-29 01:09:14.6945 [ERROR] [StorageTypeMgr]::CreateWrapperForType(262) - Failed to create wrapper for type 'Color': Exception has been thrown by the target of an invocation.
2025-05-29 01:09:14.6945 [WARN] [StorageTypeMetadata]::LoadWithInlineMetadata(150) - Failed to convert inline value for key 'ColorValue'
2025-05-29 01:09:14.6945 [ERROR] [StorageTypeMgr]::CreateWrapperForType(262) - Failed to create wrapper for type 'Int32[]': Exception has been thrown by the target of an invocation.
2025-05-29 01:09:14.6945 [WARN] [StorageTypeMetadata]::LoadWithInlineMetadata(150) - Failed to convert inline value for key 'IntArray'
2025-05-29 01:09:14.7061 [ERROR] [StorageTypeMgr]::CreateWrapperForType(262) - Failed to create wrapper for type 'List`1': Exception has been thrown by the target of an invocation.
2025-05-29 01:09:14.7061 [WARN] [StorageTypeMetadata]::LoadWithInlineMetadata(150) - Failed to convert inline value for key 'StringList'
2025-05-29 01:09:14.7061 [ERROR] [StorageTypeMgr]::CreateWrapperForType(262) - Failed to create wrapper for type 'Dictionary`2': Exception has been thrown by the target of an invocation.
2025-05-29 01:09:14.7061 [WARN] [StorageTypeMetadata]::LoadWithInlineMetadata(150) - Failed to convert inline value for key 'StringIntDict'
2025-05-29 01:09:14.7061 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(159) - Loaded 0 items from inline metadata format
2025-05-29 01:09:14.7061 [INFO] [StorageCache]::LoadFromDictionary(216) - Loaded 0 items into cache
2025-05-29 01:09:14.7061 [INFO] [StorageInstance]::LoadFromFileSync(457) - Instance [PlayerPrefsEncrypted] - Data loaded synchronously, 10 items
2025-05-29 01:09:14.7061 [INFO] [StorageComprehensiveTest]::TestStorageInstance(327) - PlayerPrefs + AES加密: 数据加载成功
2025-05-29 01:09:14.7061 [WARN] [StorageCache]::TryGet(140) - Key 'IntValue' not found
2025-05-29 01:09:14.7061 [ERROR] [StorageComprehensiveTest]::VerifyTestData(393) - 无法读取IntValue
2025-05-29 01:09:14.7061 [ERROR] [StorageComprehensiveTest]::TestStorageInstance(343) - PlayerPrefs + AES加密: ✗ 数据验证失败，数据不一致
2025-05-29 01:09:14.7061 [INFO] [StorageComprehensiveTest]::TestStorageInstance(351) - --- 测试完成: PlayerPrefs + AES加密 ---

2025-05-29 01:09:14.7061 [INFO] [StorageComprehensiveTest]::TestStorageInstance(300) - --- 开始测试: PlayerPrefs + 无加密 ---
2025-05-29 01:09:14.7061 [ERROR] [StorageComprehensiveTest]::TestStorageInstance(348) - PlayerPrefs + 无加密: 测试过程中发生异常: 0
1
2025-05-29 01:09:14.7061 [INFO] [StorageComprehensiveTest]::TestStorageInstance(351) - --- 测试完成: PlayerPrefs + 无加密 ---

2025-05-29 01:09:14.7061 [INFO] [StorageComprehensiveTest]::TestStorageInstance(300) - --- 开始测试: File + AES加密 ---
2025-05-29 01:09:14.7243 [ERROR] [StorageComprehensiveTest]::TestStorageInstance(348) - File + AES加密: 测试过程中发生异常: 0
1
2025-05-29 01:09:14.7243 [INFO] [StorageComprehensiveTest]::TestStorageInstance(351) - --- 测试完成: File + AES加密 ---

2025-05-29 01:09:14.7243 [INFO] [StorageComprehensiveTest]::TestStorageInstance(300) - --- 开始测试: File + 无加密 ---
2025-05-29 01:09:14.7243 [ERROR] [StorageComprehensiveTest]::TestStorageInstance(348) - File + 无加密: 测试过程中发生异常: 0
1
2025-05-29 01:09:14.7243 [INFO] [StorageComprehensiveTest]::TestStorageInstance(351) - --- 测试完成: File + 无加密 ---

2025-05-29 01:09:14.7243 [INFO] [StorageComprehensiveTest]::TestErrorHandling(478) - --- 开始测试: 错误处理 ---
2025-05-29 01:09:14.7243 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: NonExistent
2025-05-29 01:09:14.7243 [INFO] [StorageInstance]::LoadFromFileSync(441) - Instance [NonExistent] - Starting sync load operation
2025-05-29 01:09:14.7243 [INFO] [Storage]::CheckAndRecoverFile(361) - All backup files do not exist! file path: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\NonExistentFile.json.
2025-05-29 01:09:14.7243 [WARN] [Storage]::ReadFromFileWithRecovery(296) - File does not exist: NonExistentFile.json
2025-05-29 01:09:14.7243 [ERROR] [StorageInstance]::LoadFromFileSync(462) - Instance [NonExistent] - Failed to load data synchronously: File is empty or does not exist, filePath: NonExistentFile.json
2025-05-29 01:09:14.7243 [INFO] [StorageComprehensiveTest]::TestErrorHandling(492) - ✓ 正确处理了不存在文件的情况
2025-05-29 01:09:14.7243 [WARN] [StorageCache]::TryGet(140) - Key 'NonExistentKey' not found
2025-05-29 01:09:14.7243 [INFO] [StorageComprehensiveTest]::TestErrorHandling(499) - ✓ 默认值处理正确
2025-05-29 01:09:14.7374 [ERROR] [StorageComprehensiveTest]::TestErrorHandling(529) - 错误处理测试中发生异常: Exception has been thrown by the target of an invocation.
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <58af53cbf2a1404e8a3caa8c7c295fd8>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <58af53cbf2a1404e8a3caa8c7c295fd8>:0 
  at Storage.StorageTypeMgr.CreateWrapperUsingReflection (System.Type targetType) [0x0002d] in H:\DiceGame\DGame\Assets\Scripts\Framework\Storage\Core\StorageTypeMgr.cs:278 
  at Storage.StorageTypeMgr+<>c__DisplayClass19_0.<CreateFactoryDelegate>b__0 () [0x00000] in H:\DiceGame\DGame\Assets\Scripts\Framework\Storage\Core\StorageTypeMgr.cs:397 
  at Storage.StorageTypeMgr.CreateWrapper[T] () [0x00022] in H:\DiceGame\DGame\Assets\Scripts\Framework\Storage\Core\StorageTypeMgr.cs:207 
  at Storage.StorageTypeMgr.CreateWrapper[T] (T value) [0x00001] in H:\DiceGame\DGame\Assets\Scripts\Framework\Storage\Core\StorageTypeMgr.cs:221 
  at Storage.StorageCache.Set[T] (System.String key, T value) [0x00052] in H:\DiceGame\DGame\Assets\Scripts\Framework\Storage\Core\StorageCache.cs:103 
  at Storage.StorageInstance.Set[T] (System.String key, T value) [0x00065] in H:\DiceGame\DGame\Assets\Scripts\Framework\Storage\StorageInstance.cs:101 
  at Storage.Test.StorageComprehensiveTest.TestErrorHandling () [0x000cd] in H:\DiceGame\DGame\Assets\Scripts\Framework\Storage\Test\StorageComprehensiveTest.cs:511 
2025-05-29 01:09:14.7374 [INFO] [StorageComprehensiveTest]::TestErrorHandling(532) - --- 测试完成: 错误处理 ---

2025-05-29 01:09:14.7374 [INFO] [StorageComprehensiveTest]::TestDataPersistence(540) - --- 开始测试: 跨会话数据持久性 ---
2025-05-29 01:09:14.7374 [ERROR] [StorageComprehensiveTest]::TestDataPersistence(582) - 跨会话测试中发生异常: Exception has been thrown by the target of an invocation.
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <58af53cbf2a1404e8a3caa8c7c295fd8>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <58af53cbf2a1404e8a3caa8c7c295fd8>:0 
  at Storage.StorageTypeMgr.CreateWrapperUsingReflection (System.Type targetType) [0x0002d] in H:\DiceGame\DGame\Assets\Scripts\Framework\Storage\Core\StorageTypeMgr.cs:278 
  at Storage.StorageTypeMgr+<>c__DisplayClass19_0.<CreateFactoryDelegate>b__0 () [0x00000] in H:\DiceGame\DGame\Assets\Scripts\Framework\Storage\Core\StorageTypeMgr.cs:397 
  at Storage.StorageTypeMgr.CreateWrapper[T] () [0x00022] in H:\DiceGame\DGame\Assets\Scripts\Framework\Storage\Core\StorageTypeMgr.cs:207 
  at Storage.StorageTypeMgr.CreateWrapper[T] (T value) [0x00001] in H:\DiceGame\DGame\Assets\Scripts\Framework\Storage\Core\StorageTypeMgr.cs:221 
  at Storage.StorageCache.Set[T] (System.String key, T value) [0x00052] in H:\DiceGame\DGame\Assets\Scripts\Framework\Storage\Core\StorageCache.cs:103 
  at Storage.StorageInstance.Set[T] (System.String key, T value) [0x00065] in H:\DiceGame\DGame\Assets\Scripts\Framework\Storage\StorageInstance.cs:101 
  at Storage.Test.StorageComprehensiveTest.TestDataPersistence () [0x0003e] in H:\DiceGame\DGame\Assets\Scripts\Framework\Storage\Test\StorageComprehensiveTest.cs:548 
2025-05-29 01:09:14.7374 [INFO] [StorageComprehensiveTest]::TestDataPersistence(585) - --- 测试完成: 跨会话数据持久性 ---

2025-05-29 01:09:14.7374 [INFO] [StorageComprehensiveTest]::RunAllTests(292) - 所有测试用例执行完成
2025-05-29 01:09:14.7374 [INFO] [StorageComprehensiveTest]::StartComprehensiveTest(168) - === Storage模块综合测试完成 ===
2025-05-29 01:09:24.7616 [INFO] [StorageManager]::OnApplicationFocusChanged(331) - Application lost focus, saving all storage instances...
2025-05-29 01:09:24.7616 [INFO] [StorageInstance]::SaveToFileSync(376) - Instance [PlayerPrefsEncrypted] - Cache is not dirty, skipping file save operation
2025-05-29 01:09:24.7616 [INFO] [StorageInstance]::SaveToFileSync(376) - Instance [PlayerPrefsPlain] - Cache is not dirty, skipping file save operation
2025-05-29 01:09:24.7616 [INFO] [StorageInstance]::SaveToFileSync(376) - Instance [FileEncrypted] - Cache is not dirty, skipping file save operation
2025-05-29 01:09:24.7616 [INFO] [StorageInstance]::SaveToFileSync(376) - Instance [FilePlain] - Cache is not dirty, skipping file save operation
2025-05-29 01:09:24.7616 [INFO] [StorageInstance]::SaveToFileSync(376) - Instance [NonExistent] - Cache is not dirty, skipping file save operation
2025-05-29 01:09:24.7616 [INFO] [StorageManager]::SaveAllInstances(283) - Save operation initiated for 5 instances
2025-05-29 01:10:23.1718 [INFO] [StorageManager]::OnApplicationQuitting(318) - Application quitting, saving all storage instances...
2025-05-29 01:10:23.1718 [INFO] [StorageInstance]::SaveToFileSync(376) - Instance [PlayerPrefsEncrypted] - Cache is not dirty, skipping file save operation
2025-05-29 01:10:23.1718 [INFO] [StorageInstance]::SaveToFileSync(376) - Instance [PlayerPrefsPlain] - Cache is not dirty, skipping file save operation
2025-05-29 01:10:23.1718 [INFO] [StorageInstance]::SaveToFileSync(376) - Instance [FileEncrypted] - Cache is not dirty, skipping file save operation
2025-05-29 01:10:23.1718 [INFO] [StorageInstance]::SaveToFileSync(376) - Instance [FilePlain] - Cache is not dirty, skipping file save operation
2025-05-29 01:10:23.1718 [INFO] [StorageInstance]::SaveToFileSync(376) - Instance [NonExistent] - Cache is not dirty, skipping file save operation
2025-05-29 01:10:23.1718 [INFO] [StorageManager]::SaveAllInstances(283) - Save operation initiated for 5 instances
2025-05-29 01:10:23.1718 [INFO] [StorageCache]::Clear(182) - Cleared 0 items from cache
2025-05-29 01:10:23.1718 [INFO] [StorageInstance]::Dispose(1188) - Storage instance disposed: PlayerPrefsEncrypted
2025-05-29 01:10:23.1718 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: PlayerPrefsEncrypted
2025-05-29 01:10:23.1718 [INFO] [StorageCache]::Clear(182) - Cleared 0 items from cache
2025-05-29 01:10:23.1718 [INFO] [StorageInstance]::Dispose(1188) - Storage instance disposed: PlayerPrefsPlain
2025-05-29 01:10:23.1718 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: PlayerPrefsPlain
2025-05-29 01:10:23.1752 [INFO] [StorageCache]::Clear(182) - Cleared 0 items from cache
2025-05-29 01:10:23.1752 [INFO] [StorageInstance]::Dispose(1188) - Storage instance disposed: FileEncrypted
2025-05-29 01:10:23.1752 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: FileEncrypted
2025-05-29 01:10:23.1752 [INFO] [StorageCache]::Clear(182) - Cleared 0 items from cache
2025-05-29 01:10:23.1752 [INFO] [StorageInstance]::Dispose(1188) - Storage instance disposed: FilePlain
2025-05-29 01:10:23.1752 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: FilePlain
