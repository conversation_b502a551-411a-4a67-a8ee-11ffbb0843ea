using System;
using UnityEngine;
using Storage;
using DGame.Framework;

namespace Storage.Example
{
    /// <summary>
    /// Unity struct 类型转换示例
    /// 展示修复后的 StorageTypeConverter 如何处理各种 Unity struct 类型转换场景
    /// </summary>
    public class UnityStructConversionExample : MonoBehaviour
    {
        [Header("示例配置")]
        [SerializeField] private bool _runOnStart = true;
        [SerializeField] private bool _enableDetailedLogging = true;

        #region Unity 生命周期

        private void Start()
        {
            if (_runOnStart)
            {
                RunExamples();
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 运行所有示例
        /// </summary>
        [ContextMenu("Run All Examples")]
        public void RunExamples()
        {
            NLogger.Log("Starting Unity struct conversion examples...");

            BasicUnityStructConversion();
            CrossTypeConversion();
            StorageSystemSimulation();
            PerformanceComparison();

            NLogger.Log("All examples completed!");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 基础 Unity struct 类型转换示例
        /// </summary>
        private void BasicUnityStructConversion()
        {
            NLogger.Log("=== Basic Unity Struct Conversion ===");

            // 1. Vector3 转换示例
            var playerPosition = new Vector3(10.5f, 0f, 25.3f);
            object boxedPosition = playerPosition; // 模拟从存储系统读取的装箱值

            if (StorageTypeConverter.TryConvert<object, Vector3>(boxedPosition, out Vector3 convertedPosition))
            {
                NLogger.Log("Player position conversion successful: {0}", arg0: convertedPosition.ToString());
            }
            else
            {
                NLogger.LogError("Player position conversion failed!");
            }

            // 2. Quaternion 转换示例
            var cameraRotation = Quaternion.Euler(15f, 45f, 0f);
            object boxedRotation = cameraRotation;

            if (StorageTypeConverter.TryConvert<object, Quaternion>(boxedRotation, out Quaternion convertedRotation))
            {
                NLogger.Log("Camera rotation conversion successful: {0}", arg0: convertedRotation.ToString());
            }
            else
            {
                NLogger.LogError("Camera rotation conversion failed!");
            }

            // 3. Color 转换示例
            var uiColor = new Color(0.8f, 0.2f, 0.4f, 1.0f);
            object boxedColor = uiColor;

            if (StorageTypeConverter.TryConvert<object, Color>(boxedColor, out Color convertedColor))
            {
                NLogger.Log("UI color conversion successful: {0}", arg0: convertedColor.ToString());
            }
            else
            {
                NLogger.LogError("UI color conversion failed!");
            }
        }

        /// <summary>
        /// 跨类型转换示例
        /// </summary>
        private void CrossTypeConversion()
        {
            NLogger.Log("=== Cross-Type Conversion ===");

            // Vector2 转 Vector3
            var screenPosition = new Vector2(1920f, 1080f);
            object boxedScreenPos = screenPosition;

            if (StorageTypeConverter.TryConvert<object, Vector3>(boxedScreenPos, out Vector3 worldPosition))
            {
                NLogger.Log("Screen to world position conversion: {0} -> {1}",
                    arg0: screenPosition.ToString(), arg1: worldPosition.ToString());
            }

            // Vector3 转 Vector2
            var worldPos = new Vector3(50f, 100f, 150f);
            object boxedWorldPos = worldPos;

            if (StorageTypeConverter.TryConvert<object, Vector2>(boxedWorldPos, out Vector2 screenPos))
            {
                NLogger.Log("World to screen position conversion: {0} -> {1}",
                    arg0: worldPos.ToString(), arg1: screenPos.ToString());
            }

            // Color 转 Vector4
            var materialColor = new Color(1.0f, 0.5f, 0.2f, 0.8f);
            object boxedMaterialColor = materialColor;

            if (StorageTypeConverter.TryConvert<object, Vector4>(boxedMaterialColor, out Vector4 colorVector))
            {
                NLogger.Log("Color to Vector4 conversion: {0} -> {1}",
                    arg0: materialColor.ToString(), arg1: colorVector.ToString());
            }
        }

        /// <summary>
        /// 存储系统模拟
        /// </summary>
        private void StorageSystemSimulation()
        {
            NLogger.Log("=== Storage System Simulation ===");

            // 模拟存储和读取游戏数据
            var gameData = new GameData
            {
                PlayerPosition = new Vector3(123.45f, 67.89f, 234.56f),
                CameraRotation = Quaternion.LookRotation(Vector3.forward, Vector3.up),
                UIThemeColor = Color.cyan,
                InventorySize = new Vector2Int(10, 5)
            };

            // 模拟保存到存储系统（值被装箱）
            object storedPosition = gameData.PlayerPosition;
            object storedRotation = gameData.CameraRotation;
            object storedColor = gameData.UIThemeColor;
            object storedInventorySize = gameData.InventorySize;

            // 模拟从存储系统读取（需要转换回具体类型）
            var loadedGameData = new GameData();

            if (StorageTypeConverter.TryConvert<object, Vector3>(storedPosition, out Vector3 loadedPosition))
            {
                loadedGameData.PlayerPosition = loadedPosition;
                NLogger.Log("Loaded player position: {0}", arg0: loadedPosition.ToString());
            }

            if (StorageTypeConverter.TryConvert<object, Quaternion>(storedRotation, out Quaternion loadedRotation))
            {
                loadedGameData.CameraRotation = loadedRotation;
                NLogger.Log("Loaded camera rotation: {0}", arg0: loadedRotation.ToString());
            }

            if (StorageTypeConverter.TryConvert<object, Color>(storedColor, out Color loadedColor))
            {
                loadedGameData.UIThemeColor = loadedColor;
                NLogger.Log("Loaded UI color: {0}", arg0: loadedColor.ToString());
            }

            if (StorageTypeConverter.TryConvert<object, Vector2Int>(storedInventorySize, out Vector2Int loadedInventorySize))
            {
                loadedGameData.InventorySize = loadedInventorySize;
                NLogger.Log("Loaded inventory size: {0}", arg0: loadedInventorySize.ToString());
            }

            // 验证数据完整性
            bool dataIntegrityValid =
                loadedGameData.PlayerPosition == gameData.PlayerPosition &&
                loadedGameData.CameraRotation == gameData.CameraRotation &&
                loadedGameData.UIThemeColor == gameData.UIThemeColor &&
                loadedGameData.InventorySize == gameData.InventorySize;

            NLogger.Log("Data integrity check: {0}", arg0: dataIntegrityValid ? "PASSED" : "FAILED");
        }

        /// <summary>
        /// 性能对比示例
        /// </summary>
        private void PerformanceComparison()
        {
            if (!_enableDetailedLogging) return;

            NLogger.Log("=== Performance Comparison ===");

            const int testIterations = 10000;
            var testVector = new Vector3(1.0f, 2.0f, 3.0f);
            object boxedVector = testVector;

            // 测试新的转换方法
            var startTime = DateTime.Now;
            for (int i = 0; i < testIterations; i++)
            {
                StorageTypeConverter.TryConvert<object, Vector3>(boxedVector, out Vector3 _);
            }
            var newMethodTime = (DateTime.Now - startTime).TotalMilliseconds;

            // 测试传统转换方法（直接拆箱）
            startTime = DateTime.Now;
            for (int i = 0; i < testIterations; i++)
            {
                var _ = (Vector3)boxedVector;
            }
            var directCastTime = (DateTime.Now - startTime).TotalMilliseconds;

            NLogger.Log("Performance comparison over {0} iterations:", arg0: testIterations);
            NLogger.Log("- StorageTypeConverter: {0:F3} ms", arg0: newMethodTime);
            NLogger.Log("- Direct cast: {0:F3} ms", arg0: directCastTime);
            NLogger.Log("- Overhead: {0:F3} ms ({1:F1}%)",
                arg0: newMethodTime - directCastTime,
                arg1: ((newMethodTime - directCastTime) / directCastTime) * 100);
        }

        #endregion

        #region 内部类

        /// <summary>
        /// 游戏数据示例类
        /// </summary>
        private class GameData
        {
            public Vector3 PlayerPosition { get; set; }
            public Quaternion CameraRotation { get; set; }
            public Color UIThemeColor { get; set; }
            public Vector2Int InventorySize { get; set; }
        }

        #endregion
    }
}