# Unity Struct 类型转换修复文档

## 概述

本次修复解决了 `StorageTypeConverter.cs` 中 `TryConvert<TFrom, TTo>` 方法无法正确处理 Unity struct 类型转换的问题。修复后的转换器现在能够正确处理装箱的 Unity struct 值到目标类型的转换，同时保持了原有的性能优化特性。

## 修复的主要问题

### 1. 类型检查逻辑缺陷
**问题**：原来只检查编译时类型 `typeof(TFrom)`，当 `TFrom` 是 `object` 时无法获取装箱值的实际类型。

**解决方案**：
- 添加了 `fromValue.GetType()` 获取运行时类型
- 同时检查编译时类型和运行时类型
- 优先使用运行时类型进行转换判断

### 2. 缺少 Unity Struct 专门处理
**问题**：没有专门的 Unity struct 类型识别和转换逻辑。

**解决方案**：
- 添加了 `IsUnityStructType()` 方法识别 Unity 常用 struct 类型
- 新增 `TryConvertUnityStruct()` 方法专门处理 Unity struct 转换
- 支持的类型包括：Vector2、Vector3、Vector4、Quaternion、Color、Color32、Rect、Bounds、Matrix4x4、Vector2Int、Vector3Int、RectInt、BoundsInt

### 3. 装箱值处理不当
**问题**：装箱的 struct 值无法正确识别和转换。

**解决方案**：
- 在类型匹配检查中增加了运行时类型匹配分支
- 使用 `(TTo)(object)fromValue` 进行直接拆箱转换
- 避免了不必要的装箱拆箱操作

## 新增功能特性

### 1. 运行时类型匹配
```csharp
// 检查运行时类型匹配（处理装箱的情况）
if (actualSourceType == targetType)
{
    // 直接拆箱转换，避免额外开销
    toValue = (TTo)(object)fromValue;
    return true;
}
```

### 2. Unity Struct 类型识别
```csharp
private static bool IsUnityStructType(Type type)
{
    return type == typeof(Vector2) || type == typeof(Vector3) || type == typeof(Vector4) ||
           type == typeof(Quaternion) || type == typeof(Color) || type == typeof(Color32) ||
           type == typeof(Rect) || type == typeof(Bounds) || type == typeof(Matrix4x4) ||
           type == typeof(Vector2Int) || type == typeof(Vector3Int) ||
           type == typeof(RectInt) || type == typeof(BoundsInt);
}
```

### 3. Vector 类型间转换
支持以下转换：
- Vector2 ↔ Vector3
- Vector2 ↔ Vector4
- Vector3 ↔ Vector4
- Vector2Int ↔ Vector3Int
- Vector2Int ↔ Vector2
- Vector3Int ↔ Vector3
- Quaternion ↔ Vector4
- Color ↔ Vector4

### 4. Color 类型转换
支持以下转换：
- Color ↔ Color32
- Color ↔ Vector4

### 5. JSON 字符串解析
支持从 JSON 字符串反序列化 Unity struct 类型：
```csharp
string jsonVector3 = "{\"x\":1.0,\"y\":2.0,\"z\":3.0}";
object jsonString = jsonVector3;
bool success = StorageTypeConverter.TryConvert<object, Vector3>(jsonString, out Vector3 result);
```

## 使用示例

### 基本用法
```csharp
// 装箱的 Vector3 转换为 Vector3
var originalVector3 = new Vector3(1.5f, 2.5f, 3.5f);
object boxedVector3 = originalVector3;

bool success = StorageTypeConverter.TryConvert<object, Vector3>(boxedVector3, out Vector3 result);
// success = true, result = (1.5, 2.5, 3.5)
```

### 跨类型转换
```csharp
// Vector2 转 Vector3
var vector2 = new Vector2(10f, 20f);
object boxedVector2 = vector2;

bool success = StorageTypeConverter.TryConvert<object, Vector3>(boxedVector2, out Vector3 result);
// success = true, result = (10.0, 20.0, 0.0)
```

### 存储系统集成
```csharp
// 模拟存储系统场景
var playerPosition = new Vector3(100f, 50f, 200f);
object storedValue = playerPosition; // 存储中的装箱值

// 从存储中读取并转换
bool success = StorageTypeConverter.TryConvert<object, Vector3>(storedValue, out Vector3 loadedPosition);
// success = true, loadedPosition = (100.0, 50.0, 200.0)
```

## 性能优化

### 1. 零拷贝转换
使用 `Unsafe.As<T, U>()` 进行零开销类型转换：
```csharp
toValue = Unsafe.As<Vector3, TTo>(ref result);
```

### 2. 运行时类型缓存
减少重复的类型检查开销。

### 3. 避免装箱拆箱
通过智能类型匹配减少不必要的装箱拆箱操作。

### 4. 内联优化
关键转换方法使用 `[MethodImpl(MethodImplOptions.AggressiveInlining)]` 标记。

## 测试验证

### 自动化测试
运行 `StorageTypeConverterTest.cs` 中的测试用例：
```csharp
var test = FindObjectOfType<StorageTypeConverterTest>();
test.RunAllTests();
```

### 示例演示
运行 `UnityStructConversionExample.cs` 查看完整示例：
```csharp
var example = FindObjectOfType<UnityStructConversionExample>();
example.RunExamples();
```

## 兼容性说明

### 向后兼容
- 保持了原有的 API 接口不变
- 现有代码无需修改即可获得新功能
- 性能开销最小化，不影响现有性能

### Unity 版本
- 兼容 Unity 2021.3 及以上版本
- 支持所有主流平台（PC、Mac、Android、iOS）

## 错误处理

### 日志记录
转换失败时会记录详细的日志信息：
```csharp
NLogger.LogWarning("Unity struct conversion failed from {0} to {1}: {2}",
    arg0: actualSourceType.Name, arg1: targetType.Name, arg2: ex.Message);
```

### 异常安全
所有转换操作都包装在 try-catch 块中，确保不会抛出未处理的异常。

## 注意事项

1. **精度损失**：某些类型转换可能存在精度损失（如浮点数到整数）
2. **逻辑合理性**：跨类型转换遵循逻辑规则（如 Vector2 转 Vector3 时 z 分量为 0）
3. **性能考虑**：虽然经过优化，但仍建议在性能敏感的代码中直接使用类型转换

## 未来扩展

可以考虑添加更多 Unity 类型的支持：
- AnimationCurve
- Gradient
- LayerMask
- 自定义 Serializable 结构体

通过 `IsUnityStructType()` 和相应的转换方法可以轻松扩展支持的类型。