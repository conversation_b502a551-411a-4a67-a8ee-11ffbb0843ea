#if UNITY_EDITOR
using UnityEditor;
using Storage.Test;

namespace Storage.Editor
{
    /// <summary>
    /// StorageTypeMgr测试菜单
    /// </summary>
    public static class StorageTypeMgrTestMenu
    {
        [MenuItem("Tools/Storage/Run Quick Verification Test")]
        public static void RunQuickTest()
        {
            StorageTypeMgrQuickTest.RunQuickTest();
        }

        [MenuItem("Tools/Storage/Run Type Manager Performance Test")]
        public static void RunPerformanceTest()
        {
            StorageTypeMgrPerformanceTest.RunAllPerformanceTests();
        }

        [MenuItem("Tools/Storage/Initialize Type Manager")]
        public static void InitializeTypeManager()
        {
            StorageTypeMgr.Initialize();
            UnityEngine.Debug.Log($"StorageTypeMgr initialized with {StorageTypeMgr.GetRegisteredTypeCount()} registered types");
        }

        [MenuItem("Tools/Storage/Show Registered Types")]
        public static void ShowRegisteredTypes()
        {
            var types = StorageTypeMgr.GetRegisteredTypes();
            UnityEngine.Debug.Log($"Registered types ({types.Count}):");
            foreach (var type in types)
            {
                UnityEngine.Debug.Log($"  - {type.Name} ({type.FullName})");
            }
        }

        [MenuItem("Tools/Storage/Test Custom Type Registration")]
        public static void TestCustomTypeRegistration()
        {
            // 测试注册自定义类型
            StorageTypeMgr.RegisterType<System.TimeSpan>();

            // 测试注册自定义工厂函数
            StorageTypeMgr.RegisterType<System.DateTime>();

            UnityEngine.Debug.Log("Custom types registered successfully");

            // 测试创建
            var timeSpanWrapper = StorageTypeMgr.CreateWrapperForType(typeof(System.TimeSpan), System.TimeSpan.FromMinutes(5));
            var dateTimeWrapper = StorageTypeMgr.CreateWrapperForType(typeof(System.DateTime), System.DateTime.Now);

            UnityEngine.Debug.Log($"TimeSpan wrapper created: {timeSpanWrapper?.GetValueString()}");
            UnityEngine.Debug.Log($"DateTime wrapper created: {dateTimeWrapper?.GetValueString()}");
        }
    }
}
#endif
