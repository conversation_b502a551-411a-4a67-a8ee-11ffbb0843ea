using System;
using System.Collections.Generic;
using System.Diagnostics;
using UnityEngine;
using DGame.Framework;

namespace Storage.Test
{
    /// <summary>
    /// StorageTypeMgr性能测试类
    /// 用于验证优化后的性能提升
    /// </summary>
    public static class StorageTypeMgrPerformanceTest
    {
        #region 测试配置

        private const int WARMUP_ITERATIONS = 1000;
        private const int TEST_ITERATIONS = 10000;
        private const int STRESS_TEST_ITERATIONS = 100000;

        #endregion

        #region 公共测试方法

        /// <summary>
        /// 运行所有性能测试
        /// </summary>
        public static void RunAllPerformanceTests()
        {
            NLogger.Log("=== StorageTypeMgr 性能测试开始 ===");

            // 初始化测试
            InitializeTest();

            // 基础性能测试
            TestBasicTypeCreation();
            TestUnityTypeCreation();
            TestCollectionTypeCreation();

            // 对比测试
            TestFactoryVsReflectionPerformance();

            // 压力测试
            TestStressPerformance();

            // 内存测试
            TestMemoryUsage();

            NLogger.Log("=== StorageTypeMgr 性能测试完成 ===");
        }

        #endregion

        #region 私有测试方法

        /// <summary>
        /// 初始化测试
        /// </summary>
        private static void InitializeTest()
        {
            NLogger.Log("--- 初始化测试 ---");

            var stopwatch = Stopwatch.StartNew();
            StorageTypeMgr.Initialize();
            stopwatch.Stop();

            var registeredCount = StorageTypeMgr.GetRegisteredTypeCount();
            NLogger.Log("初始化完成，耗时: {0}ms，已注册类型数量: {1}", 
                arg0: stopwatch.ElapsedMilliseconds, arg1: registeredCount);
        }

        /// <summary>
        /// 测试基础类型创建性能
        /// </summary>
        private static void TestBasicTypeCreation()
        {
            NLogger.Log("--- 基础类型创建性能测试 ---");

            // 预热
            for (int i = 0; i < WARMUP_ITERATIONS; i++)
            {
                var wrapper = StorageTypeMgr.CreateWrapperForType(typeof(int), 42);
                wrapper?.GetBoxedValue();
            }

            // 测试int类型
            var stopwatch = Stopwatch.StartNew();
            for (int i = 0; i < TEST_ITERATIONS; i++)
            {
                var wrapper = StorageTypeMgr.CreateWrapperForType(typeof(int), i);
                wrapper?.GetBoxedValue();
            }
            stopwatch.Stop();
            NLogger.Log("int类型创建 {0} 次，耗时: {1}ms，平均: {2:F4}ms", 
                arg0: TEST_ITERATIONS, arg1: stopwatch.ElapsedMilliseconds, 
                arg2: (double)stopwatch.ElapsedMilliseconds / TEST_ITERATIONS);

            // 测试float类型
            stopwatch.Restart();
            for (int i = 0; i < TEST_ITERATIONS; i++)
            {
                var wrapper = StorageTypeMgr.CreateWrapperForType(typeof(float), (float)i);
                wrapper?.GetBoxedValue();
            }
            stopwatch.Stop();
            NLogger.Log("float类型创建 {0} 次，耗时: {1}ms，平均: {2:F4}ms", 
                arg0: TEST_ITERATIONS, arg1: stopwatch.ElapsedMilliseconds, 
                arg2: (double)stopwatch.ElapsedMilliseconds / TEST_ITERATIONS);

            // 测试string类型
            stopwatch.Restart();
            for (int i = 0; i < TEST_ITERATIONS; i++)
            {
                var wrapper = StorageTypeMgr.CreateWrapperForType(typeof(string), $"test_{i}");
                wrapper?.GetBoxedValue();
            }
            stopwatch.Stop();
            NLogger.Log("string类型创建 {0} 次，耗时: {1}ms，平均: {2:F4}ms", 
                arg0: TEST_ITERATIONS, arg1: stopwatch.ElapsedMilliseconds, 
                arg2: (double)stopwatch.ElapsedMilliseconds / TEST_ITERATIONS);
        }

        /// <summary>
        /// 测试Unity类型创建性能
        /// </summary>
        private static void TestUnityTypeCreation()
        {
            NLogger.Log("--- Unity类型创建性能测试 ---");

            // 测试Vector3类型
            var stopwatch = Stopwatch.StartNew();
            for (int i = 0; i < TEST_ITERATIONS; i++)
            {
                var wrapper = StorageTypeMgr.CreateWrapperForType(typeof(Vector3), Vector3.one * i);
                wrapper?.GetBoxedValue();
            }
            stopwatch.Stop();
            NLogger.Log("Vector3类型创建 {0} 次，耗时: {1}ms，平均: {2:F4}ms", 
                arg0: TEST_ITERATIONS, arg1: stopwatch.ElapsedMilliseconds, 
                arg2: (double)stopwatch.ElapsedMilliseconds / TEST_ITERATIONS);

            // 测试Quaternion类型
            stopwatch.Restart();
            for (int i = 0; i < TEST_ITERATIONS; i++)
            {
                var wrapper = StorageTypeMgr.CreateWrapperForType(typeof(Quaternion), Quaternion.identity);
                wrapper?.GetBoxedValue();
            }
            stopwatch.Stop();
            NLogger.Log("Quaternion类型创建 {0} 次，耗时: {1}ms，平均: {2:F4}ms", 
                arg0: TEST_ITERATIONS, arg1: stopwatch.ElapsedMilliseconds, 
                arg2: (double)stopwatch.ElapsedMilliseconds / TEST_ITERATIONS);
        }

        /// <summary>
        /// 测试集合类型创建性能
        /// </summary>
        private static void TestCollectionTypeCreation()
        {
            NLogger.Log("--- 集合类型创建性能测试 ---");

            // 测试int[]类型
            var intArray = new int[] { 1, 2, 3, 4, 5 };
            var stopwatch = Stopwatch.StartNew();
            for (int i = 0; i < TEST_ITERATIONS; i++)
            {
                var wrapper = StorageTypeMgr.CreateWrapperForType(typeof(int[]), intArray);
                wrapper?.GetBoxedValue();
            }
            stopwatch.Stop();
            NLogger.Log("int[]类型创建 {0} 次，耗时: {1}ms，平均: {2:F4}ms", 
                arg0: TEST_ITERATIONS, arg1: stopwatch.ElapsedMilliseconds, 
                arg2: (double)stopwatch.ElapsedMilliseconds / TEST_ITERATIONS);

            // 测试List<int>类型
            var intList = new List<int> { 1, 2, 3, 4, 5 };
            stopwatch.Restart();
            for (int i = 0; i < TEST_ITERATIONS; i++)
            {
                var wrapper = StorageTypeMgr.CreateWrapperForType(typeof(List<int>), intList);
                wrapper?.GetBoxedValue();
            }
            stopwatch.Stop();
            NLogger.Log("List<int>类型创建 {0} 次，耗时: {1}ms，平均: {2:F4}ms", 
                arg0: TEST_ITERATIONS, arg1: stopwatch.ElapsedMilliseconds, 
                arg2: (double)stopwatch.ElapsedMilliseconds / TEST_ITERATIONS);
        }

        /// <summary>
        /// 测试工厂函数与反射的性能对比
        /// </summary>
        private static void TestFactoryVsReflectionPerformance()
        {
            NLogger.Log("--- 工厂函数 vs 反射性能对比测试 ---");

            // 测试已注册类型（使用工厂函数）
            var stopwatch = Stopwatch.StartNew();
            for (int i = 0; i < TEST_ITERATIONS; i++)
            {
                var wrapper = StorageTypeMgr.CreateWrapperForType(typeof(int), i);
                wrapper?.GetBoxedValue();
            }
            stopwatch.Stop();
            var factoryTime = stopwatch.ElapsedMilliseconds;
            NLogger.Log("已注册类型（工厂函数）创建 {0} 次，耗时: {1}ms", 
                arg0: TEST_ITERATIONS, arg1: factoryTime);

            // 测试未注册类型（使用Expression委托）
            stopwatch.Restart();
            for (int i = 0; i < TEST_ITERATIONS; i++)
            {
                var wrapper = StorageTypeMgr.CreateWrapperForType(typeof(TimeSpan), TimeSpan.FromSeconds(i));
                wrapper?.GetBoxedValue();
            }
            stopwatch.Stop();
            var expressionTime = stopwatch.ElapsedMilliseconds;
            NLogger.Log("未注册类型（Expression委托）创建 {0} 次，耗时: {1}ms", 
                arg0: TEST_ITERATIONS, arg1: expressionTime);

            var improvement = (double)expressionTime / factoryTime;
            NLogger.Log("性能对比：Expression委托相对于工厂函数的倍数: {0:F2}x", arg0: improvement);
        }

        /// <summary>
        /// 压力测试
        /// </summary>
        private static void TestStressPerformance()
        {
            NLogger.Log("--- 压力测试 ---");

            var stopwatch = Stopwatch.StartNew();
            for (int i = 0; i < STRESS_TEST_ITERATIONS; i++)
            {
                var wrapper = StorageTypeMgr.CreateWrapperForType(typeof(int), i);
                wrapper?.GetBoxedValue();
            }
            stopwatch.Stop();

            NLogger.Log("压力测试：创建 {0} 个包装器，耗时: {1}ms，平均: {2:F6}ms", 
                arg0: STRESS_TEST_ITERATIONS, arg1: stopwatch.ElapsedMilliseconds, 
                arg2: (double)stopwatch.ElapsedMilliseconds / STRESS_TEST_ITERATIONS);
        }

        /// <summary>
        /// 内存使用测试
        /// </summary>
        private static void TestMemoryUsage()
        {
            NLogger.Log("--- 内存使用测试 ---");

            // 强制垃圾回收
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            var beforeMemory = GC.GetTotalMemory(false);

            // 创建大量包装器
            var wrappers = new List<StorageTypeWrapper>(TEST_ITERATIONS);
            for (int i = 0; i < TEST_ITERATIONS; i++)
            {
                var wrapper = StorageTypeMgr.CreateWrapperForType(typeof(int), i);
                wrappers.Add(wrapper);
            }

            var afterMemory = GC.GetTotalMemory(false);
            var memoryUsed = afterMemory - beforeMemory;

            NLogger.Log("创建 {0} 个包装器，内存使用: {1} bytes，平均每个: {2:F2} bytes", 
                arg0: TEST_ITERATIONS, arg1: memoryUsed, arg2: (double)memoryUsed / TEST_ITERATIONS);

            // 清理
            wrappers.Clear();
            GC.Collect();
        }

        #endregion
    }
}
