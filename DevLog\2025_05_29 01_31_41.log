2025-05-29 01:31:41.8393 [INFO] [StorageComprehensiveTest]::StartComprehensiveTest(152) - === 开始Storage模块综合测试 ===
2025-05-29 01:31:41.8483 [INFO] [StorageComprehensiveTest]::InitializeTestData(200) - 测试数据初始化完成:
2025-05-29 01:31:41.8483 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(595) -   IntValue: 42
2025-05-29 01:31:41.8483 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(596) -   FloatValue: 3.14159
2025-05-29 01:31:41.8483 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(597) -   StringValue: Hello Storage!
2025-05-29 01:31:41.8483 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(598) -   BoolValue: True
2025-05-29 01:31:41.8483 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(599) -   Vector3Value: (1.50, 2.50, 3.50)
2025-05-29 01:31:41.8483 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(600) -   QuaternionValue: (0.65328, -0.27060, 0.65328, 0.27060)
2025-05-29 01:31:41.8483 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(601) -   ColorValue: RGBA(1.000, 0.000, 0.000, 1.000)
2025-05-29 01:31:41.8624 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(602) -   IntArray: [1, 2, 3, 4, 5]
2025-05-29 01:31:41.8624 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(603) -   StringList: [Item1, Item2, Item3]
2025-05-29 01:31:41.8624 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(604) -   StringIntDict: 3 items
2025-05-29 01:31:41.8624 [INFO] [Storage]::InitializeCore(68) - Storage core initialized successfully
2025-05-29 01:31:41.8624 [INFO] [StorageSettings]::InitializePathCache(71) - Storage paths cached successfully
2025-05-29 01:31:41.8624 [INFO] [StorageManager]::Initialize(88) - StorageManager initialized successfully
2025-05-29 01:31:41.8624 [INFO] [StorageComprehensiveTest]::InitializeStorageManager(213) - StorageManager初始化完成
2025-05-29 01:31:41.8624 [INFO] [StorageComprehensiveTest]::CreateTestInstances(226) - 创建测试存储实例...
2025-05-29 01:31:41.8624 [INFO] [StorageTypeMgr]::Initialize(76) - StorageTypeMgr initialized with 35 registered types
2025-05-29 01:31:41.8624 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: PlayerPrefsEncrypted
2025-05-29 01:31:41.8624 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: PlayerPrefsPlain
2025-05-29 01:31:41.8624 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: FileEncrypted
2025-05-29 01:31:41.8624 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: FilePlain
2025-05-29 01:31:41.8624 [INFO] [StorageComprehensiveTest]::CreateTestInstances(264) - 所有测试实例创建完成
2025-05-29 01:31:41.8624 [INFO] [StorageComprehensiveTest]::RunAllTests(272) - 开始执行测试用例...
2025-05-29 01:31:41.8624 [INFO] [StorageComprehensiveTest]::TestStorageInstance(300) - --- 开始测试: PlayerPrefs + AES加密 ---
2025-05-29 01:31:41.8624 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: IntValue, Type: Int32, Value: 42
2025-05-29 01:31:41.8624 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: FloatValue, Type: Single, Value: 3.14159
2025-05-29 01:31:41.8624 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: StringValue, Type: String, Value: Hello Storage!
2025-05-29 01:31:41.8624 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: BoolValue, Type: Boolean, Value: True
2025-05-29 01:31:41.8777 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: Vector3Value, Type: Vector3, Value: (1.50, 2.50, 3.50)
2025-05-29 01:31:41.8777 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: QuaternionValue, Type: Quaternion, Value: (0.65328, -0.27060, 0.65328, 0.27060)
2025-05-29 01:31:41.8777 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: ColorValue, Type: Color, Value: RGBA(1.000, 0.000, 0.000, 1.000)
2025-05-29 01:31:41.8777 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: IntArray, Type: Int32[], Value: [1, 2, 3, 4, 5]
2025-05-29 01:31:41.8777 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: StringList, Type: List`1, Value: ["Item1", "Item2", "Item3"]
2025-05-29 01:31:41.8777 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsEncrypted] - Key: StringIntDict, Type: Dictionary`2, Value: {"Score": 1000, "Level": 5, "Lives": 3}
2025-05-29 01:31:41.8777 [INFO] [StorageComprehensiveTest]::SaveTestData(377) - 测试数据已保存到实例: PlayerPrefsEncrypted
2025-05-29 01:31:41.8777 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(119) - Generated inline metadata for 10 items
2025-05-29 01:31:41.8777 [INFO] [StorageInstance]::SaveToFileSync(386) - Instance [PlayerPrefsEncrypted] - Starting sync save operation
2025-05-29 01:31:41.9407 [INFO] [Storage]::WriteToFileWithBackup(242) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\StorageTest_PlayerPrefs_Encrypted
2025-05-29 01:31:41.9407 [INFO] [StorageInstance]::SaveToFileSync(408) - Instance [PlayerPrefsEncrypted] - Data saved synchronously
2025-05-29 01:31:41.9407 [INFO] [StorageComprehensiveTest]::TestStorageInstance(311) - PlayerPrefs + AES加密: 数据保存成功
2025-05-29 01:31:41.9407 [INFO] [StorageCache]::Clear(182) - Cleared 10 items from cache
2025-05-29 01:31:41.9407 [INFO] [StorageInstance]::Clear(227) - Instance [PlayerPrefsEncrypted] - All data cleared from cache
2025-05-29 01:31:41.9407 [INFO] [StorageComprehensiveTest]::TestStorageInstance(321) - PlayerPrefs + AES加密: 内存缓存已清空
2025-05-29 01:31:41.9407 [INFO] [StorageInstance]::LoadFromFileSync(441) - Instance [PlayerPrefsEncrypted] - Starting sync load operation
2025-05-29 01:31:41.9625 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'IntValue' with type 'Int32' from inline format
2025-05-29 01:31:41.9625 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'FloatValue' with type 'Single' from inline format
2025-05-29 01:31:41.9625 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'StringValue' with type 'String' from inline format
2025-05-29 01:31:41.9625 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'BoolValue' with type 'Boolean' from inline format
2025-05-29 01:31:41.9625 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'Vector3Value' with type 'Vector3' from inline format
2025-05-29 01:31:41.9625 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'QuaternionValue' with type 'Quaternion' from inline format
2025-05-29 01:31:41.9716 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'ColorValue' with type 'Color' from inline format
2025-05-29 01:31:41.9716 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'IntArray' with type 'Int32[]' from inline format
2025-05-29 01:31:41.9716 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'StringList' with type 'List`1' from inline format
2025-05-29 01:31:41.9716 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'StringIntDict' with type 'Dictionary`2' from inline format
2025-05-29 01:31:41.9716 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(159) - Loaded 10 items from inline metadata format
2025-05-29 01:31:41.9716 [INFO] [StorageCache]::LoadFromDictionary(216) - Loaded 10 items into cache
2025-05-29 01:31:41.9716 [INFO] [StorageInstance]::LoadFromFileSync(457) - Instance [PlayerPrefsEncrypted] - Data loaded synchronously, 10 items
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::TestStorageInstance(327) - PlayerPrefs + AES加密: 数据加载成功
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::VerifyTestData(458) - 原始数据:
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(595) -   IntValue: 42
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(596) -   FloatValue: 3.14159
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(597) -   StringValue: Hello Storage!
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(598) -   BoolValue: True
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(599) -   Vector3Value: (1.50, 2.50, 3.50)
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(600) -   QuaternionValue: (0.65328, -0.27060, 0.65328, 0.27060)
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(601) -   ColorValue: RGBA(1.000, 0.000, 0.000, 1.000)
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(602) -   IntArray: [1, 2, 3, 4, 5]
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(603) -   StringList: [Item1, Item2, Item3]
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(604) -   StringIntDict: 3 items
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::VerifyTestData(460) - 加载数据:
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(595) -   IntValue: 42
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(596) -   FloatValue: 3.14159
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(597) -   StringValue: Hello Storage!
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(598) -   BoolValue: True
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(599) -   Vector3Value: (1.50, 2.50, 3.50)
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(600) -   QuaternionValue: (0.65328, -0.27060, 0.65328, 0.27060)
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(601) -   ColorValue: RGBA(1.000, 0.000, 0.000, 1.000)
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(602) -   IntArray: [1, 2, 3, 4, 5]
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(603) -   StringList: [Item1, Item2, Item3]
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(604) -   StringIntDict: 3 items
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::TestStorageInstance(339) - PlayerPrefs + AES加密: ✓ 数据验证成功，所有数据完整
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::TestStorageInstance(351) - --- 测试完成: PlayerPrefs + AES加密 ---

2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::TestStorageInstance(300) - --- 开始测试: PlayerPrefs + 无加密 ---
2025-05-29 01:31:41.9716 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsPlain] - Key: IntValue, Type: Int32, Value: 42
2025-05-29 01:31:41.9716 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsPlain] - Key: FloatValue, Type: Single, Value: 3.14159
2025-05-29 01:31:41.9716 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsPlain] - Key: StringValue, Type: String, Value: Hello Storage!
2025-05-29 01:31:41.9716 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsPlain] - Key: BoolValue, Type: Boolean, Value: True
2025-05-29 01:31:41.9716 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsPlain] - Key: Vector3Value, Type: Vector3, Value: (1.50, 2.50, 3.50)
2025-05-29 01:31:41.9716 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsPlain] - Key: QuaternionValue, Type: Quaternion, Value: (0.65328, -0.27060, 0.65328, 0.27060)
2025-05-29 01:31:41.9716 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsPlain] - Key: ColorValue, Type: Color, Value: RGBA(1.000, 0.000, 0.000, 1.000)
2025-05-29 01:31:41.9716 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsPlain] - Key: IntArray, Type: Int32[], Value: [1, 2, 3, 4, 5]
2025-05-29 01:31:41.9716 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsPlain] - Key: StringList, Type: List`1, Value: ["Item1", "Item2", "Item3"]
2025-05-29 01:31:41.9716 [INFO] [StorageInstance]::Set(102) - Instance [PlayerPrefsPlain] - Key: StringIntDict, Type: Dictionary`2, Value: {"Score": 1000, "Level": 5, "Lives": 3}
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::SaveTestData(377) - 测试数据已保存到实例: PlayerPrefsPlain
2025-05-29 01:31:41.9716 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(119) - Generated inline metadata for 10 items
2025-05-29 01:31:41.9716 [INFO] [StorageInstance]::SaveToFileSync(386) - Instance [PlayerPrefsPlain] - Starting sync save operation
2025-05-29 01:31:41.9716 [INFO] [Storage]::WriteToFileWithBackup(242) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\StorageTest_PlayerPrefs_Plain
2025-05-29 01:31:41.9716 [INFO] [StorageInstance]::SaveToFileSync(408) - Instance [PlayerPrefsPlain] - Data saved synchronously
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::TestStorageInstance(311) - PlayerPrefs + 无加密: 数据保存成功
2025-05-29 01:31:41.9716 [INFO] [StorageCache]::Clear(182) - Cleared 10 items from cache
2025-05-29 01:31:41.9716 [INFO] [StorageInstance]::Clear(227) - Instance [PlayerPrefsPlain] - All data cleared from cache
2025-05-29 01:31:41.9716 [INFO] [StorageComprehensiveTest]::TestStorageInstance(321) - PlayerPrefs + 无加密: 内存缓存已清空
2025-05-29 01:31:41.9873 [INFO] [StorageInstance]::LoadFromFileSync(441) - Instance [PlayerPrefsPlain] - Starting sync load operation
2025-05-29 01:31:41.9873 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'IntValue' with type 'Int32' from inline format
2025-05-29 01:31:41.9873 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'FloatValue' with type 'Single' from inline format
2025-05-29 01:31:41.9873 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'StringValue' with type 'String' from inline format
2025-05-29 01:31:41.9873 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'BoolValue' with type 'Boolean' from inline format
2025-05-29 01:31:41.9873 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'Vector3Value' with type 'Vector3' from inline format
2025-05-29 01:31:41.9873 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'QuaternionValue' with type 'Quaternion' from inline format
2025-05-29 01:31:41.9873 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'ColorValue' with type 'Color' from inline format
2025-05-29 01:31:41.9873 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'IntArray' with type 'Int32[]' from inline format
2025-05-29 01:31:41.9873 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'StringList' with type 'List`1' from inline format
2025-05-29 01:31:41.9873 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'StringIntDict' with type 'Dictionary`2' from inline format
2025-05-29 01:31:41.9873 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(159) - Loaded 10 items from inline metadata format
2025-05-29 01:31:41.9873 [INFO] [StorageCache]::LoadFromDictionary(216) - Loaded 10 items into cache
2025-05-29 01:31:41.9873 [INFO] [StorageInstance]::LoadFromFileSync(457) - Instance [PlayerPrefsPlain] - Data loaded synchronously, 10 items
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::TestStorageInstance(327) - PlayerPrefs + 无加密: 数据加载成功
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::VerifyTestData(458) - 原始数据:
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(595) -   IntValue: 42
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(596) -   FloatValue: 3.14159
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(597) -   StringValue: Hello Storage!
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(598) -   BoolValue: True
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(599) -   Vector3Value: (1.50, 2.50, 3.50)
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(600) -   QuaternionValue: (0.65328, -0.27060, 0.65328, 0.27060)
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(601) -   ColorValue: RGBA(1.000, 0.000, 0.000, 1.000)
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(602) -   IntArray: [1, 2, 3, 4, 5]
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(603) -   StringList: [Item1, Item2, Item3]
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(604) -   StringIntDict: 3 items
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::VerifyTestData(460) - 加载数据:
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(595) -   IntValue: 42
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(596) -   FloatValue: 3.14159
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(597) -   StringValue: Hello Storage!
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(598) -   BoolValue: True
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(599) -   Vector3Value: (1.50, 2.50, 3.50)
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(600) -   QuaternionValue: (0.65328, -0.27060, 0.65328, 0.27060)
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(601) -   ColorValue: RGBA(1.000, 0.000, 0.000, 1.000)
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(602) -   IntArray: [1, 2, 3, 4, 5]
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(603) -   StringList: [Item1, Item2, Item3]
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(604) -   StringIntDict: 3 items
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::TestStorageInstance(339) - PlayerPrefs + 无加密: ✓ 数据验证成功，所有数据完整
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::TestStorageInstance(351) - --- 测试完成: PlayerPrefs + 无加密 ---

2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::TestStorageInstance(300) - --- 开始测试: File + AES加密 ---
2025-05-29 01:31:41.9873 [INFO] [StorageInstance]::Set(102) - Instance [FileEncrypted] - Key: IntValue, Type: Int32, Value: 42
2025-05-29 01:31:41.9873 [INFO] [StorageInstance]::Set(102) - Instance [FileEncrypted] - Key: FloatValue, Type: Single, Value: 3.14159
2025-05-29 01:31:41.9873 [INFO] [StorageInstance]::Set(102) - Instance [FileEncrypted] - Key: StringValue, Type: String, Value: Hello Storage!
2025-05-29 01:31:41.9873 [INFO] [StorageInstance]::Set(102) - Instance [FileEncrypted] - Key: BoolValue, Type: Boolean, Value: True
2025-05-29 01:31:41.9873 [INFO] [StorageInstance]::Set(102) - Instance [FileEncrypted] - Key: Vector3Value, Type: Vector3, Value: (1.50, 2.50, 3.50)
2025-05-29 01:31:41.9873 [INFO] [StorageInstance]::Set(102) - Instance [FileEncrypted] - Key: QuaternionValue, Type: Quaternion, Value: (0.65328, -0.27060, 0.65328, 0.27060)
2025-05-29 01:31:41.9873 [INFO] [StorageInstance]::Set(102) - Instance [FileEncrypted] - Key: ColorValue, Type: Color, Value: RGBA(1.000, 0.000, 0.000, 1.000)
2025-05-29 01:31:41.9873 [INFO] [StorageInstance]::Set(102) - Instance [FileEncrypted] - Key: IntArray, Type: Int32[], Value: [1, 2, 3, 4, 5]
2025-05-29 01:31:41.9873 [INFO] [StorageInstance]::Set(102) - Instance [FileEncrypted] - Key: StringList, Type: List`1, Value: ["Item1", "Item2", "Item3"]
2025-05-29 01:31:41.9873 [INFO] [StorageInstance]::Set(102) - Instance [FileEncrypted] - Key: StringIntDict, Type: Dictionary`2, Value: {"Score": 1000, "Level": 5, "Lives": 3}
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::SaveTestData(377) - 测试数据已保存到实例: FileEncrypted
2025-05-29 01:31:41.9873 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(119) - Generated inline metadata for 10 items
2025-05-29 01:31:41.9873 [INFO] [StorageInstance]::SaveToFileSync(386) - Instance [FileEncrypted] - Starting sync save operation
2025-05-29 01:31:41.9873 [INFO] [Storage]::WriteToFileWithBackup(242) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\StorageTest_File_Encrypted.json
2025-05-29 01:31:41.9873 [INFO] [StorageInstance]::SaveToFileSync(408) - Instance [FileEncrypted] - Data saved synchronously
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::TestStorageInstance(311) - File + AES加密: 数据保存成功
2025-05-29 01:31:41.9873 [INFO] [StorageCache]::Clear(182) - Cleared 10 items from cache
2025-05-29 01:31:41.9873 [INFO] [StorageInstance]::Clear(227) - Instance [FileEncrypted] - All data cleared from cache
2025-05-29 01:31:41.9873 [INFO] [StorageComprehensiveTest]::TestStorageInstance(321) - File + AES加密: 内存缓存已清空
2025-05-29 01:31:41.9873 [INFO] [StorageInstance]::LoadFromFileSync(441) - Instance [FileEncrypted] - Starting sync load operation
2025-05-29 01:31:41.9873 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'IntValue' with type 'Int32' from inline format
2025-05-29 01:31:41.9873 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'FloatValue' with type 'Single' from inline format
2025-05-29 01:31:41.9873 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'StringValue' with type 'String' from inline format
2025-05-29 01:31:41.9873 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'BoolValue' with type 'Boolean' from inline format
2025-05-29 01:31:41.9873 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'Vector3Value' with type 'Vector3' from inline format
2025-05-29 01:31:41.9873 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'QuaternionValue' with type 'Quaternion' from inline format
2025-05-29 01:31:41.9873 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'ColorValue' with type 'Color' from inline format
2025-05-29 01:31:41.9873 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'IntArray' with type 'Int32[]' from inline format
2025-05-29 01:31:41.9873 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'StringList' with type 'List`1' from inline format
2025-05-29 01:31:42.0029 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'StringIntDict' with type 'Dictionary`2' from inline format
2025-05-29 01:31:42.0029 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(159) - Loaded 10 items from inline metadata format
2025-05-29 01:31:42.0029 [INFO] [StorageCache]::LoadFromDictionary(216) - Loaded 10 items into cache
2025-05-29 01:31:42.0029 [INFO] [StorageInstance]::LoadFromFileSync(457) - Instance [FileEncrypted] - Data loaded synchronously, 10 items
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::TestStorageInstance(327) - File + AES加密: 数据加载成功
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::VerifyTestData(458) - 原始数据:
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(595) -   IntValue: 42
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(596) -   FloatValue: 3.14159
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(597) -   StringValue: Hello Storage!
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(598) -   BoolValue: True
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(599) -   Vector3Value: (1.50, 2.50, 3.50)
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(600) -   QuaternionValue: (0.65328, -0.27060, 0.65328, 0.27060)
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(601) -   ColorValue: RGBA(1.000, 0.000, 0.000, 1.000)
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(602) -   IntArray: [1, 2, 3, 4, 5]
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(603) -   StringList: [Item1, Item2, Item3]
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(604) -   StringIntDict: 3 items
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::VerifyTestData(460) - 加载数据:
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(595) -   IntValue: 42
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(596) -   FloatValue: 3.14159
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(597) -   StringValue: Hello Storage!
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(598) -   BoolValue: True
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(599) -   Vector3Value: (1.50, 2.50, 3.50)
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(600) -   QuaternionValue: (0.65328, -0.27060, 0.65328, 0.27060)
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(601) -   ColorValue: RGBA(1.000, 0.000, 0.000, 1.000)
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(602) -   IntArray: [1, 2, 3, 4, 5]
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(603) -   StringList: [Item1, Item2, Item3]
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(604) -   StringIntDict: 3 items
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::TestStorageInstance(339) - File + AES加密: ✓ 数据验证成功，所有数据完整
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::TestStorageInstance(351) - --- 测试完成: File + AES加密 ---

2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::TestStorageInstance(300) - --- 开始测试: File + 无加密 ---
2025-05-29 01:31:42.0029 [INFO] [StorageInstance]::Set(102) - Instance [FilePlain] - Key: IntValue, Type: Int32, Value: 42
2025-05-29 01:31:42.0029 [INFO] [StorageInstance]::Set(102) - Instance [FilePlain] - Key: FloatValue, Type: Single, Value: 3.14159
2025-05-29 01:31:42.0029 [INFO] [StorageInstance]::Set(102) - Instance [FilePlain] - Key: StringValue, Type: String, Value: Hello Storage!
2025-05-29 01:31:42.0029 [INFO] [StorageInstance]::Set(102) - Instance [FilePlain] - Key: BoolValue, Type: Boolean, Value: True
2025-05-29 01:31:42.0029 [INFO] [StorageInstance]::Set(102) - Instance [FilePlain] - Key: Vector3Value, Type: Vector3, Value: (1.50, 2.50, 3.50)
2025-05-29 01:31:42.0029 [INFO] [StorageInstance]::Set(102) - Instance [FilePlain] - Key: QuaternionValue, Type: Quaternion, Value: (0.65328, -0.27060, 0.65328, 0.27060)
2025-05-29 01:31:42.0029 [INFO] [StorageInstance]::Set(102) - Instance [FilePlain] - Key: ColorValue, Type: Color, Value: RGBA(1.000, 0.000, 0.000, 1.000)
2025-05-29 01:31:42.0029 [INFO] [StorageInstance]::Set(102) - Instance [FilePlain] - Key: IntArray, Type: Int32[], Value: [1, 2, 3, 4, 5]
2025-05-29 01:31:42.0029 [INFO] [StorageInstance]::Set(102) - Instance [FilePlain] - Key: StringList, Type: List`1, Value: ["Item1", "Item2", "Item3"]
2025-05-29 01:31:42.0029 [INFO] [StorageInstance]::Set(102) - Instance [FilePlain] - Key: StringIntDict, Type: Dictionary`2, Value: {"Score": 1000, "Level": 5, "Lives": 3}
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::SaveTestData(377) - 测试数据已保存到实例: FilePlain
2025-05-29 01:31:42.0029 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(119) - Generated inline metadata for 10 items
2025-05-29 01:31:42.0029 [INFO] [StorageInstance]::SaveToFileSync(386) - Instance [FilePlain] - Starting sync save operation
2025-05-29 01:31:42.0029 [INFO] [Storage]::WriteToFileWithBackup(242) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\StorageTest_File_Plain.json
2025-05-29 01:31:42.0029 [INFO] [StorageInstance]::SaveToFileSync(408) - Instance [FilePlain] - Data saved synchronously
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::TestStorageInstance(311) - File + 无加密: 数据保存成功
2025-05-29 01:31:42.0029 [INFO] [StorageCache]::Clear(182) - Cleared 10 items from cache
2025-05-29 01:31:42.0029 [INFO] [StorageInstance]::Clear(227) - Instance [FilePlain] - All data cleared from cache
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::TestStorageInstance(321) - File + 无加密: 内存缓存已清空
2025-05-29 01:31:42.0029 [INFO] [StorageInstance]::LoadFromFileSync(441) - Instance [FilePlain] - Starting sync load operation
2025-05-29 01:31:42.0029 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'IntValue' with type 'Int32' from inline format
2025-05-29 01:31:42.0029 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'FloatValue' with type 'Single' from inline format
2025-05-29 01:31:42.0029 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'StringValue' with type 'String' from inline format
2025-05-29 01:31:42.0029 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'BoolValue' with type 'Boolean' from inline format
2025-05-29 01:31:42.0029 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'Vector3Value' with type 'Vector3' from inline format
2025-05-29 01:31:42.0029 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'QuaternionValue' with type 'Quaternion' from inline format
2025-05-29 01:31:42.0029 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'ColorValue' with type 'Color' from inline format
2025-05-29 01:31:42.0029 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'IntArray' with type 'Int32[]' from inline format
2025-05-29 01:31:42.0029 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'StringList' with type 'List`1' from inline format
2025-05-29 01:31:42.0029 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'StringIntDict' with type 'Dictionary`2' from inline format
2025-05-29 01:31:42.0029 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(159) - Loaded 10 items from inline metadata format
2025-05-29 01:31:42.0029 [INFO] [StorageCache]::LoadFromDictionary(216) - Loaded 10 items into cache
2025-05-29 01:31:42.0029 [INFO] [StorageInstance]::LoadFromFileSync(457) - Instance [FilePlain] - Data loaded synchronously, 10 items
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::TestStorageInstance(327) - File + 无加密: 数据加载成功
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::VerifyTestData(458) - 原始数据:
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(595) -   IntValue: 42
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(596) -   FloatValue: 3.14159
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(597) -   StringValue: Hello Storage!
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(598) -   BoolValue: True
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(599) -   Vector3Value: (1.50, 2.50, 3.50)
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(600) -   QuaternionValue: (0.65328, -0.27060, 0.65328, 0.27060)
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(601) -   ColorValue: RGBA(1.000, 0.000, 0.000, 1.000)
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(602) -   IntArray: [1, 2, 3, 4, 5]
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(603) -   StringList: [Item1, Item2, Item3]
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(604) -   StringIntDict: 3 items
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::VerifyTestData(460) - 加载数据:
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(595) -   IntValue: 42
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(596) -   FloatValue: 3.14159
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(597) -   StringValue: Hello Storage!
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(598) -   BoolValue: True
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(599) -   Vector3Value: (1.50, 2.50, 3.50)
2025-05-29 01:31:42.0029 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(600) -   QuaternionValue: (0.65328, -0.27060, 0.65328, 0.27060)
2025-05-29 01:31:42.0185 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(601) -   ColorValue: RGBA(1.000, 0.000, 0.000, 1.000)
2025-05-29 01:31:42.0185 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(602) -   IntArray: [1, 2, 3, 4, 5]
2025-05-29 01:31:42.0185 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(603) -   StringList: [Item1, Item2, Item3]
2025-05-29 01:31:42.0185 [INFO] [StorageComprehensiveTest]::LogTestDataDetails(604) -   StringIntDict: 3 items
2025-05-29 01:31:42.0185 [INFO] [StorageComprehensiveTest]::TestStorageInstance(339) - File + 无加密: ✓ 数据验证成功，所有数据完整
2025-05-29 01:31:42.0185 [INFO] [StorageComprehensiveTest]::TestStorageInstance(351) - --- 测试完成: File + 无加密 ---

2025-05-29 01:31:42.0185 [INFO] [StorageComprehensiveTest]::TestErrorHandling(478) - --- 开始测试: 错误处理 ---
2025-05-29 01:31:42.0185 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: NonExistent
2025-05-29 01:31:42.0185 [INFO] [StorageInstance]::LoadFromFileSync(441) - Instance [NonExistent] - Starting sync load operation
2025-05-29 01:31:42.0185 [INFO] [Storage]::CheckAndRecoverFile(361) - All backup files do not exist! file path: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\NonExistentFile.json.
2025-05-29 01:31:42.0185 [WARN] [Storage]::ReadFromFileWithRecovery(296) - File does not exist: NonExistentFile.json
2025-05-29 01:31:42.0185 [ERROR] [StorageInstance]::LoadFromFileSync(462) - Instance [NonExistent] - Failed to load data synchronously: File is empty or does not exist, filePath: NonExistentFile.json
2025-05-29 01:31:42.0185 [INFO] [StorageComprehensiveTest]::TestErrorHandling(492) - ✓ 正确处理了不存在文件的情况
2025-05-29 01:31:42.0185 [WARN] [StorageCache]::TryGet(140) - Key 'NonExistentKey' not found
2025-05-29 01:31:42.0185 [INFO] [StorageComprehensiveTest]::TestErrorHandling(499) - ✓ 默认值处理正确
2025-05-29 01:31:42.0185 [INFO] [StorageInstance]::Set(102) - Instance [FileEncrypted] - Key: TestKey, Type: String, Value: TestValue
2025-05-29 01:31:42.0185 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(119) - Generated inline metadata for 11 items
2025-05-29 01:31:42.0185 [INFO] [StorageInstance]::SaveToFileSync(386) - Instance [FileEncrypted] - Starting sync save operation
2025-05-29 01:31:42.0185 [INFO] [Storage]::WriteToFileWithBackup(242) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\StorageTest_File_Encrypted.json
2025-05-29 01:31:42.0185 [INFO] [StorageInstance]::SaveToFileSync(408) - Instance [FileEncrypted] - Data saved synchronously
2025-05-29 01:31:42.0185 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: WrongPassword
2025-05-29 01:31:42.0185 [INFO] [StorageInstance]::LoadFromFileSync(441) - Instance [WrongPassword] - Starting sync load operation
2025-05-29 01:31:42.0185 [INFO] [Storage]::CheckAndRecoverFile(361) - All backup files do not exist! file path: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\EncryptedTest.json.
2025-05-29 01:31:42.0185 [WARN] [Storage]::ReadFromFileWithRecovery(296) - File does not exist: EncryptedTest.json
2025-05-29 01:31:42.0185 [ERROR] [StorageInstance]::LoadFromFileSync(462) - Instance [WrongPassword] - Failed to load data synchronously: File is empty or does not exist, filePath: EncryptedTest.json
2025-05-29 01:31:42.0185 [INFO] [StorageComprehensiveTest]::TestErrorHandling(520) - ✓ 正确处理了密码错误的情况
2025-05-29 01:31:42.0185 [INFO] [StorageInstance]::SaveToFileSync(376) - Instance [NonExistent] - Cache is not dirty, skipping file save operation
2025-05-29 01:31:42.0185 [INFO] [StorageCache]::Clear(182) - Cleared 0 items from cache
2025-05-29 01:31:42.0185 [INFO] [StorageInstance]::Dispose(1188) - Storage instance disposed: NonExistent
2025-05-29 01:31:42.0185 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: NonExistent
2025-05-29 01:31:42.0185 [INFO] [StorageInstance]::SaveToFileSync(376) - Instance [WrongPassword] - Cache is not dirty, skipping file save operation
2025-05-29 01:31:42.0185 [INFO] [StorageCache]::Clear(182) - Cleared 0 items from cache
2025-05-29 01:31:42.0185 [INFO] [StorageInstance]::Dispose(1188) - Storage instance disposed: WrongPassword
2025-05-29 01:31:42.0185 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: WrongPassword
2025-05-29 01:31:42.0185 [INFO] [StorageComprehensiveTest]::TestErrorHandling(532) - --- 测试完成: 错误处理 ---

2025-05-29 01:31:42.0185 [INFO] [StorageComprehensiveTest]::TestDataPersistence(540) - --- 开始测试: 跨会话数据持久性 ---
2025-05-29 01:31:42.0185 [INFO] [StorageInstance]::Set(102) - Instance [FilePlain] - Key: PersistenceKey, Type: String, Value: PersistenceTest_638840791020276170
2025-05-29 01:31:42.0185 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(119) - Generated inline metadata for 11 items
2025-05-29 01:31:42.0185 [INFO] [StorageInstance]::SaveToFileSync(386) - Instance [FilePlain] - Starting sync save operation
2025-05-29 01:31:42.0185 [INFO] [Storage]::WriteToFileWithBackup(242) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\StorageTest_File_Plain.json
2025-05-29 01:31:42.0185 [INFO] [StorageInstance]::SaveToFileSync(408) - Instance [FilePlain] - Data saved synchronously
2025-05-29 01:31:42.0185 [INFO] [StorageInstance]::SaveToFileSync(376) - Instance [FilePlain] - Cache is not dirty, skipping file save operation
2025-05-29 01:31:42.0185 [INFO] [StorageCache]::Clear(182) - Cleared 11 items from cache
2025-05-29 01:31:42.0185 [INFO] [StorageInstance]::Dispose(1188) - Storage instance disposed: FilePlain
2025-05-29 01:31:42.0185 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: FilePlain
2025-05-29 01:31:42.0185 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: FilePlain_New
2025-05-29 01:31:42.0185 [INFO] [StorageInstance]::LoadFromFileSync(441) - Instance [FilePlain_New] - Starting sync load operation
2025-05-29 01:31:42.0185 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'IntValue' with type 'Int32' from inline format
2025-05-29 01:31:42.0185 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'FloatValue' with type 'Single' from inline format
2025-05-29 01:31:42.0185 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'StringValue' with type 'String' from inline format
2025-05-29 01:31:42.0185 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'BoolValue' with type 'Boolean' from inline format
2025-05-29 01:31:42.0185 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'Vector3Value' with type 'Vector3' from inline format
2025-05-29 01:31:42.0185 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'QuaternionValue' with type 'Quaternion' from inline format
2025-05-29 01:31:42.0346 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'ColorValue' with type 'Color' from inline format
2025-05-29 01:31:42.0346 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'IntArray' with type 'Int32[]' from inline format
2025-05-29 01:31:42.0346 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'StringList' with type 'List`1' from inline format
2025-05-29 01:31:42.0346 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'StringIntDict' with type 'Dictionary`2' from inline format
2025-05-29 01:31:42.0346 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(145) - Loaded key 'PersistenceKey' with type 'String' from inline format
2025-05-29 01:31:42.0346 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(159) - Loaded 11 items from inline metadata format
2025-05-29 01:31:42.0346 [INFO] [StorageCache]::LoadFromDictionary(216) - Loaded 11 items into cache
2025-05-29 01:31:42.0346 [INFO] [StorageInstance]::LoadFromFileSync(457) - Instance [FilePlain_New] - Data loaded synchronously, 11 items
2025-05-29 01:31:42.0346 [INFO] [StorageComprehensiveTest]::TestDataPersistence(565) - ✓ 跨会话数据持久性测试成功
2025-05-29 01:31:42.0346 [INFO] [StorageInstance]::SaveToFileSync(376) - Instance [FilePlain_New] - Cache is not dirty, skipping file save operation
2025-05-29 01:31:42.0346 [INFO] [StorageCache]::Clear(182) - Cleared 11 items from cache
2025-05-29 01:31:42.0346 [INFO] [StorageInstance]::Dispose(1188) - Storage instance disposed: FilePlain_New
2025-05-29 01:31:42.0346 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: FilePlain_New
2025-05-29 01:31:42.0346 [INFO] [StorageComprehensiveTest]::TestDataPersistence(585) - --- 测试完成: 跨会话数据持久性 ---

2025-05-29 01:31:42.0346 [INFO] [StorageComprehensiveTest]::RunAllTests(292) - 所有测试用例执行完成
2025-05-29 01:31:42.0346 [INFO] [StorageComprehensiveTest]::StartComprehensiveTest(168) - === Storage模块综合测试完成 ===
2025-05-29 01:31:44.8839 [INFO] [StorageManager]::OnApplicationFocusChanged(331) - Application lost focus, saving all storage instances...
2025-05-29 01:31:44.8839 [INFO] [StorageInstance]::SaveToFileSync(376) - Instance [PlayerPrefsEncrypted] - Cache is not dirty, skipping file save operation
2025-05-29 01:31:44.8839 [INFO] [StorageInstance]::SaveToFileSync(376) - Instance [PlayerPrefsPlain] - Cache is not dirty, skipping file save operation
2025-05-29 01:31:44.8839 [INFO] [StorageInstance]::SaveToFileSync(376) - Instance [FileEncrypted] - Cache is not dirty, skipping file save operation
2025-05-29 01:31:44.8839 [INFO] [StorageManager]::SaveAllInstances(283) - Save operation initiated for 3 instances
2025-05-29 01:31:48.4901 [INFO] [StorageManager]::OnApplicationFocusChanged(331) - Application lost focus, saving all storage instances...
2025-05-29 01:31:48.4901 [INFO] [StorageInstance]::SaveToFileSync(376) - Instance [PlayerPrefsEncrypted] - Cache is not dirty, skipping file save operation
2025-05-29 01:31:48.4901 [INFO] [StorageInstance]::SaveToFileSync(376) - Instance [PlayerPrefsPlain] - Cache is not dirty, skipping file save operation
2025-05-29 01:31:48.4901 [INFO] [StorageInstance]::SaveToFileSync(376) - Instance [FileEncrypted] - Cache is not dirty, skipping file save operation
2025-05-29 01:31:48.4901 [INFO] [StorageManager]::SaveAllInstances(283) - Save operation initiated for 3 instances
2025-05-29 01:31:48.6109 [INFO] [StorageManager]::OnApplicationQuitting(318) - Application quitting, saving all storage instances...
2025-05-29 01:31:48.6109 [INFO] [StorageInstance]::SaveToFileSync(376) - Instance [PlayerPrefsEncrypted] - Cache is not dirty, skipping file save operation
2025-05-29 01:31:48.6109 [INFO] [StorageInstance]::SaveToFileSync(376) - Instance [PlayerPrefsPlain] - Cache is not dirty, skipping file save operation
2025-05-29 01:31:48.6109 [INFO] [StorageInstance]::SaveToFileSync(376) - Instance [FileEncrypted] - Cache is not dirty, skipping file save operation
2025-05-29 01:31:48.6109 [INFO] [StorageManager]::SaveAllInstances(283) - Save operation initiated for 3 instances
2025-05-29 01:31:48.6124 [INFO] [StorageCache]::Clear(182) - Cleared 10 items from cache
2025-05-29 01:31:48.6124 [INFO] [StorageInstance]::Dispose(1188) - Storage instance disposed: PlayerPrefsEncrypted
2025-05-29 01:31:48.6124 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: PlayerPrefsEncrypted
2025-05-29 01:31:48.6124 [INFO] [StorageCache]::Clear(182) - Cleared 10 items from cache
2025-05-29 01:31:48.6124 [INFO] [StorageInstance]::Dispose(1188) - Storage instance disposed: PlayerPrefsPlain
2025-05-29 01:31:48.6124 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: PlayerPrefsPlain
2025-05-29 01:31:48.6124 [INFO] [StorageCache]::Clear(182) - Cleared 11 items from cache
2025-05-29 01:31:48.6124 [INFO] [StorageInstance]::Dispose(1188) - Storage instance disposed: FileEncrypted
2025-05-29 01:31:48.6124 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: FileEncrypted
