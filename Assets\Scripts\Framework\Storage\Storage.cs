using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using UnityEngine;
using Storage.Serialization;
using Storage.Encryption;
using DGame.Framework;
using Unity.VisualScripting;

namespace Storage
{
    /// <summary>
    /// 文件操作完成回调委托
    /// </summary>
    /// <param name="success">操作是否成功</param>
    /// <param name="errorMessage">错误信息（如果有）</param>
    public delegate void FileOperationCallback(bool success, string errorMessage = null);

    /// <summary>
    /// 文件读取完成回调委托
    /// </summary>
    /// <param name="success">操作是否成功</param>
    /// <param name="data">读取的数据（如果成功）</param>
    /// <param name="errorMessage">错误信息（如果有）</param>
    public delegate void FileReadCallback(bool success, Dictionary<string, object> data = null, string errorMessage = null);

    /// <summary>
    /// 存储系统核心类，提供数据序列化、加密、文件操作等核心功能
    /// 注意：此类已重构为多实例架构，请使用StorageManager来管理存储实例
    /// </summary>
    public static class Storage
    {
        #region 私有字段

        private static readonly Queue<Action> _mainThreadActions = new Queue<Action>();
        private static readonly object _mainThreadLock = new object();
        private static int _mainThreadId;

        #endregion

        #region 属性

        /// <summary>
        /// 主线程ID，用于线程安全检查
        /// </summary>
        public static int MainThreadId
        {
            get { return _mainThreadId; }
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化存储系统核心功能
        /// 注意：此方法由StorageManager调用，不应直接使用
        /// </summary>
        internal static void InitializeCore()
        {
            // 记录主线程ID
            _mainThreadId = Thread.CurrentThread.ManagedThreadId;

            // 确保主线程处理器已创建
            StorageMainThreadProcessor.EnsureCreated();

            NLogger.Log("Storage core initialized successfully");
        }

        #endregion

        #region 内部核心方法

        /// <summary>
        /// 在主线程执行操作
        /// </summary>
        /// <param name="action">要执行的操作</param>
        public static void RunOnMainThread(Action action)
        {
            if (action == null)
                return;

            if (Thread.CurrentThread.ManagedThreadId == _mainThreadId)
            {
                action();
            }
            else
            {
                lock (_mainThreadLock)
                {
                    _mainThreadActions.Enqueue(action);
                }
            }
        }

        /// <summary>
        /// 执行主线程操作队列中的操作
        /// 由StorageMainThreadProcessor.Update调用
        /// </summary>
        internal static void ExecuteMainThreadActions()
        {
            lock (_mainThreadLock)
            {
                while (_mainThreadActions.Count > 0)
                {
                    Action action = _mainThreadActions.Dequeue();
                    try
                    {
                        action();
                    }
                    catch (Exception ex)
                    {
                        NLogger.LogError("Error executing main thread action: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                    }
                }
            }
        }

        /// <summary>
        /// 处理数据序列化和加密
        /// </summary>
        /// <param name="data">要处理的数据</param>
        /// <param name="settings">存储设置</param>
        /// <returns>处理后的JSON字符串</returns>
        public static string SerializeAndEncrypt(Dictionary<string, object> data, StorageSettings settings)
        {
            // 序列化
            string json = StorageSerializer.SerializeToJson(data, settings.PrettyPrint);

            // 加密处理
            if (settings.Encryption != StorageSettings.EncryptionType.None)
            {
                json = EncryptData(json, settings);
            }

            return json;
        }

        /// <summary>
        /// 处理数据解密和反序列化
        /// </summary>
        /// <param name="json">要处理的JSON字符串</param>
        /// <param name="settings">存储设置</param>
        /// <returns>解析后的数据字典</returns>
        public static Dictionary<string, object> DecryptAndDeserialize(string json, StorageSettings settings)
        {
            if (string.IsNullOrEmpty(json))
            {
                return new Dictionary<string, object>();
            }

            // 解密处理
            if (settings.Encryption != StorageSettings.EncryptionType.None)
            {
                json = DecryptData(json, settings);
            }

            // 反序列化
            return StorageSerializer.DeserializeFromJson(json);
        }

        #endregion

        /// <summary>
        /// 加密数据
        /// </summary>
        /// <param name="data">要加密的数据</param>
        /// <param name="settings">设置对象</param>
        /// <returns>加密后的数据</returns>
        private static string EncryptData(string data, StorageSettings settings)
        {
            switch (settings.Encryption)
            {
                case StorageSettings.EncryptionType.AES:
                    return StorageEncryption.EncryptAES(data, settings.EncryptionPassword);
                case StorageSettings.EncryptionType.XOR:
                    return StorageEncryption.EncryptXOR(data, settings.EncryptionPassword);
                default:
                    return data;
            }
        }

        /// <summary>
        /// 解密数据
        /// </summary>
        /// <param name="encryptedData">加密的数据</param>
        /// <param name="settings">设置对象</param>
        /// <returns>解密后的数据</returns>
        private static string DecryptData(string encryptedData, StorageSettings settings)
        {
            switch (settings.Encryption)
            {
                case StorageSettings.EncryptionType.AES:
                    return StorageEncryption.DecryptAES(encryptedData, settings.EncryptionPassword);
                case StorageSettings.EncryptionType.XOR:
                    return StorageEncryption.DecryptXOR(encryptedData, settings.EncryptionPassword);
                default:
                    return encryptedData;
            }
        }

        /// <summary>
        /// 写入文件，使用安全备份机制
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="settings">设置对象</param>
        public static void WriteToFileWithBackup(string data, StorageSettings settings)
        {
            var filePath = settings.GetFullPath();
            var saveBackupPath = settings.GetSaveBackupPath();
            var sourceBackupPath = settings.GetSourceBackupPath();
            var directory = Path.GetDirectoryName(filePath);

            try
            {
                // 确保目录存在
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 如果保存备份文件存在，删除
                if (File.Exists(saveBackupPath))
                {
                    File.Delete(saveBackupPath);
                }
                // 先写入保存备份文件
                File.WriteAllText(saveBackupPath, data, settings.TextEncoding);

                // 如果源文件存在，先备份源文件
                if (File.Exists(filePath))
                {
                    File.Move(filePath, sourceBackupPath);
                }

                // 将保存备份文件重命名为源文件
                File.Move(saveBackupPath, filePath);
                // 删除源文件备份
                File.Delete(sourceBackupPath);

                NLogger.Log("File saved successfully: {0}", arg0: filePath);
            }
            catch (Exception ex)
            {
                // 如果保存失败，源文件备份还在
                if (File.Exists(sourceBackupPath))
                {
                    // 源文件也在，删除源文件备份
                    if (File.Exists(filePath))
                    {
                        File.Delete(sourceBackupPath);
                        NLogger.Log("Source file exists! Deleted source backup file: {0}", arg0: sourceBackupPath);
                    }
                    // 源文件不在了,使用源文件的备份恢复
                    else
                    {
                        File.Move(sourceBackupPath, filePath);
                        NLogger.Log("Source file not exists! Restored source file from backup: {0}", arg0: sourceBackupPath);
                    }
                }
                // 源文件备份不在了
                else
                {
                    // 保存备份文件存在
                    if (File.Exists(sourceBackupPath))
                    {
                        // 将保存备份文件重命名为源文件
                        File.Move(saveBackupPath, filePath);
                        NLogger.Log("Source file not exists! Restored source file from backup: {0}", arg0: filePath);
                    }
                }

                NLogger.LogError("Failed to save file: {0}\nex.Message: {1}\nStackTrace: {2}", arg0: filePath, arg1: ex.Message, arg2: ex.StackTrace);
                throw;
            }
        }

        /// <summary>
        /// 从文件读取，使用文件恢复机制
        /// </summary>
        /// <param name="settings">设置对象</param>
        /// <returns>文件内容</returns>
        public static string ReadFromFileWithRecovery(StorageSettings settings)
        {
            try
            {
                // 检查文件恢复情况，获取恢复后的文件路径
                var filePath = CheckAndRecoverFile(settings);
                // 读取文件
                if (File.Exists(filePath))
                {
                    return File.ReadAllText(filePath, settings.TextEncoding);
                }

                NLogger.LogWarning("File does not exist: {0}", arg0: settings.FilePath);
                return string.Empty;
            }
            catch (Exception ex)
            {
                NLogger.LogError("Failed to read file: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                return string.Empty;
            }
        }

        /// <summary>
        /// 检查并恢复文件
        /// </summary>
        /// <param name="filePath">主文件路径</param>
        /// <param name="backupPath">备份文件路径</param>
        private static string CheckAndRecoverFile(StorageSettings settings)
        {
            var filePath = settings.GetFullPath();
            bool mainFileExists = File.Exists(filePath);

            // 主文件存在，直接返回
            if (mainFileExists)
            {
                return filePath;
            }

            var saveBackupPath = settings.GetSaveBackupPath();
            bool saveBackupFileExists = File.Exists(saveBackupPath);

            var sourceBackupPath = settings.GetSourceBackupPath();
            bool sourceBackupFileExists = File.Exists(sourceBackupPath);

            // 源文件不存在但备份文件存在，直接恢复
            if (saveBackupFileExists)
            {
                try
                {
                    File.Move(saveBackupPath, filePath);
                    NLogger.Log("Recovered file from backup: {0}", arg0: saveBackupPath);
                    return filePath;
                }
                catch (Exception ex)
                {
                    NLogger.LogError("Failed to recover file from backup: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                    return string.Empty;
                }
            }

            // 源文件不存在，存储备份文件也不存在，从源文件备份中恢复
            if (sourceBackupFileExists)
            {
                try
                {
                    File.Move(sourceBackupPath, filePath);
                    NLogger.Log("Recovered file from backup: {0}", arg0: sourceBackupPath);
                    return filePath;
                }
                catch (Exception ex)
                {
                    NLogger.LogError("Failed to recover file from backup: {0}\nStackTrace: {1}", arg0: ex.Message, arg1: ex.StackTrace);
                    return string.Empty;
                }
            }

            // 所有存档文件都不存在，返回空字符串
            NLogger.Log("All backup files do not exist! file path: {0}.", arg0: filePath);
            return string.Empty;
        }
    }
}
