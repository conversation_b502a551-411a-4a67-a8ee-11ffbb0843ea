using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Runtime.CompilerServices;
using DGame.Framework;
using UnityEngine;

namespace Storage
{
    /// <summary>
    /// 存储类型管理器
    /// 负责创建和管理类型包装器，类似于ES3的ES3TypeMgr
    /// </summary>
    public static class StorageTypeMgr
    {
        #region 私有字段

        private static readonly object _lock = new();

        /// <summary>
        /// 工厂函数字典
        /// </summary>
        private static Dictionary<Type, Func<StorageTypeWrapper>> _typeFactories;

        /// <summary>
        /// 反射委托缓存，避免重复反射调用
        /// </summary>
        private static readonly Dictionary<Type, Func<StorageTypeWrapper>> _reflectionCache = new();

        private static bool _isInitialized = false;
        private static bool _isInitializing = false; // 防止递归初始化的标志

        #endregion

        #region 初始化

        /// <summary>
        /// 初始化类型管理器（防止递归调用）
        /// </summary>
        public static void Initialize()
        {
            if (_isInitialized)
                return;

            lock (_lock)
            {
                // 双重检查锁定模式
                if (_isInitialized)
                    return;

                // 防止递归初始化
                if (_isInitializing)
                {
                    NLogger.LogWarning("StorageTypeMgr is already initializing, skipping recursive call");
                    return;
                }

                try
                {
                    _isInitializing = true;
                    _typeFactories = new Dictionary<Type, Func<StorageTypeWrapper>>();

                    // 立即设置初始化标志，防止递归
                    _isInitialized = true;

                    // 注册基础类型（使用内部方法避免递归）
                    RegisterPrimitiveTypesInternal();

                    // 注册Unity类型（使用内部方法避免递归）
                    RegisterUnityTypesInternal();

                    // 注册集合类型（使用内部方法避免递归）
                    RegisterCollectionTypesInternal();

                    NLogger.Log("StorageTypeMgr initialized with {0} registered types", arg0: _typeFactories.Count);
                }
                finally
                {
                    _isInitializing = false;
                }
            }
        }

        /// <summary>
        /// 注册基础类型（内部方法，避免递归调用）
        /// </summary>
        private static void RegisterPrimitiveTypesInternal()
        {
            // 注册基础类型
            _typeFactories[typeof(int)] = () => new StorageTypeWrapper<int>();
            _typeFactories[typeof(float)] = () => new StorageTypeWrapper<float>();
            _typeFactories[typeof(bool)] = () => new StorageTypeWrapper<bool>();
            _typeFactories[typeof(long)] = () => new StorageTypeWrapper<long>();
            _typeFactories[typeof(double)] = () => new StorageTypeWrapper<double>();
            _typeFactories[typeof(string)] = () => new StorageTypeWrapper<string>();
            _typeFactories[typeof(byte)] = () => new StorageTypeWrapper<byte>();
            _typeFactories[typeof(sbyte)] = () => new StorageTypeWrapper<sbyte>();
            _typeFactories[typeof(short)] = () => new StorageTypeWrapper<short>();
            _typeFactories[typeof(ushort)] = () => new StorageTypeWrapper<ushort>();
            _typeFactories[typeof(uint)] = () => new StorageTypeWrapper<uint>();
            _typeFactories[typeof(ulong)] = () => new StorageTypeWrapper<ulong>();
            _typeFactories[typeof(decimal)] = () => new StorageTypeWrapper<decimal>();
            _typeFactories[typeof(char)] = () => new StorageTypeWrapper<char>();
            _typeFactories[typeof(DateTime)] = () => new StorageTypeWrapper<DateTime>();
            _typeFactories[typeof(Guid)] = () => new StorageTypeWrapper<Guid>();
        }

        /// <summary>
        /// 注册Unity类型（内部方法，避免递归调用）
        /// </summary>
        private static void RegisterUnityTypesInternal()
        {
            // 注册Unity类型
            _typeFactories[typeof(Vector2)] = () => new StorageTypeWrapper<Vector2>();
            _typeFactories[typeof(Vector3)] = () => new StorageTypeWrapper<Vector3>();
            _typeFactories[typeof(Quaternion)] = () => new StorageTypeWrapper<Quaternion>();
            _typeFactories[typeof(Color)] = () => new StorageTypeWrapper<Color>();
            _typeFactories[typeof(Rect)] = () => new StorageTypeWrapper<Rect>();
            _typeFactories[typeof(Bounds)] = () => new StorageTypeWrapper<Bounds>();
            _typeFactories[typeof(Vector4)] = () => new StorageTypeWrapper<Vector4>();
            _typeFactories[typeof(Vector2Int)] = () => new StorageTypeWrapper<Vector2Int>();
            _typeFactories[typeof(Vector3Int)] = () => new StorageTypeWrapper<Vector3Int>();
            _typeFactories[typeof(Color32)] = () => new StorageTypeWrapper<Color32>();
            _typeFactories[typeof(Matrix4x4)] = () => new StorageTypeWrapper<Matrix4x4>();
            _typeFactories[typeof(LayerMask)] = () => new StorageTypeWrapper<LayerMask>();
        }

        /// <summary>
        /// 注册集合类型（内部方法，避免递归调用）
        /// </summary>
        private static void RegisterCollectionTypesInternal()
        {
            // 注册数组类型
            _typeFactories[typeof(int[])] = () => new StorageTypeWrapper<int[]>();
            _typeFactories[typeof(float[])] = () => new StorageTypeWrapper<float[]>();
            _typeFactories[typeof(string[])] = () => new StorageTypeWrapper<string[]>();
            _typeFactories[typeof(bool[])] = () => new StorageTypeWrapper<bool[]>();

            // 注册常用List类型
            _typeFactories[typeof(List<int>)] = () => new StorageTypeWrapper<List<int>>();
            _typeFactories[typeof(List<float>)] = () => new StorageTypeWrapper<List<float>>();
            _typeFactories[typeof(List<string>)] = () => new StorageTypeWrapper<List<string>>();
            _typeFactories[typeof(List<bool>)] = () => new StorageTypeWrapper<List<bool>>();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 注册类型（安全版本，避免递归初始化）
        /// </summary>
        /// <typeparam name="T">要注册的类型</typeparam>
        /// <returns>是否注册成功</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool RegisterType<T>()
        {
            // 确保初始化完成，但避免在初始化过程中递归调用
            EnsureInitialized();

            var type = typeof(T);
            lock (_lock)
            {
                if (_typeFactories.ContainsKey(type))
                {
                    return false;
                }
                _typeFactories[type] = () => new StorageTypeWrapper<T>();
                return true;
            }
        }

        /// <summary>
        /// 取消注册类型
        /// </summary>
        /// <typeparam name="T">要取消注册的类型</typeparam>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static void UnregisterType<T>()
        {
            EnsureInitialized();

            var type = typeof(T);
            lock (_lock)
            {
                _typeFactories.Remove(type);
                _reflectionCache.Remove(type);
            }
        }

        /// <summary>
        /// 确保初始化完成（安全版本）
        /// </summary>
        private static void EnsureInitialized()
        {
            if (!_isInitialized && !_isInitializing)
            {
                Initialize();
            }
        }

        /// <summary>
        /// 创建指定类型的包装器
        /// </summary>
        /// <typeparam name="T">类型</typeparam>
        /// <returns>类型包装器</returns>
        public static StorageTypeWrapper<T> CreateWrapper<T>()
        {
            EnsureInitialized();

            if (_typeFactories.TryGetValue(typeof(T), out var emptyFactory))
            {
                return (StorageTypeWrapper<T>)emptyFactory();
            }

            // 如果没有注册的类型，尝试动态创建
            return CreateDynamicWrapper<T>();
        }

        /// <summary>
        /// 根据类型创建包装器
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>类型包装器</returns>
        public static StorageTypeWrapper CreateWrapper<T>(T value)
        {
            var wrapper = CreateWrapper<T>();
            wrapper.SetValue(value);
            return wrapper;
        }

        /// <summary>
        /// 根据指定类型和值创建包装器（用于类型元数据恢复）
        /// </summary>
        /// <param name="targetType">目标类型</param>
        /// <param name="value">值</param>
        /// <returns>类型包装器，如果创建失败则返回null</returns>
        public static StorageTypeWrapper CreateWrapperForType(Type targetType, object value)
        {
            if (targetType == null)
            {
                NLogger.LogError("Target type cannot be null");
                return null;
            }

            try
            {
                EnsureInitialized();

                // 优先使用预注册的工厂函数
                if (!_typeFactories.TryGetValue(targetType, out var factory))
                {
                    // 使用缓存的委托或创建新的委托
                    if (!_reflectionCache.TryGetValue(targetType, out factory))
                    {
                        factory = CreateFactoryDelegate(targetType);
                        _reflectionCache.Add(targetType, factory);
                    }
                }

                var wrapper = factory();
                wrapper.SetValue(value);
                return wrapper;
            }
            catch (Exception ex)
            {
                NLogger.LogError("Failed to create wrapper for type '{0}': {1}", arg0: targetType.Name, arg1: ex.Message);
                return null;
            }
        }

        /// <summary>
        /// 使用反射创建包装器（优化版本，减少装箱操作）
        /// </summary>
        /// <param name="targetType">目标类型</param>
        /// <param name="value">值</param>
        /// <returns>包装器实例</returns>
        private static StorageTypeWrapper CreateWrapperUsingReflection(Type targetType)
        {
            // 使用反射创建泛型方法
            var createMethod = typeof(StorageTypeMgr).GetMethod("CreateWrapper", new Type[0]);
            var genericMethod = createMethod.MakeGenericMethod(targetType);
            var wrapper = (StorageTypeWrapper)genericMethod.Invoke(null, null);

            if (wrapper == null)
            {
                NLogger.LogError("Failed to create wrapper for type '{0}'", arg0: targetType.Name);
                return null;
            }

            return wrapper;
        }

        /// <summary>
        /// 检查类型是否已注册
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>是否已注册</returns>
        public static bool IsTypeRegistered(Type type)
        {
            EnsureInitialized();
            return _typeFactories.ContainsKey(type);
        }

        /// <summary>
        /// 检查类型是否已注册（泛型版本）
        /// </summary>
        /// <typeparam name="T">类型</typeparam>
        /// <returns>是否已注册</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool IsTypeRegistered<T>()
        {
            return IsTypeRegistered(typeof(T));
        }

        /// <summary>
        /// 获取所有已注册的类型
        /// </summary>
        /// <returns>已注册的类型集合</returns>
        public static ICollection<Type> GetRegisteredTypes()
        {
            EnsureInitialized();
            var allTypes = new HashSet<Type>(_typeFactories.Keys);
            allTypes.UnionWith(_typeFactories.Keys);
            return allTypes;
        }

        /// <summary>
        /// 获取已注册类型的数量
        /// </summary>
        /// <returns>已注册类型的数量</returns>
        public static int GetRegisteredTypeCount()
        {
            EnsureInitialized();
            var allTypes = new HashSet<Type>(_typeFactories.Keys);
            allTypes.UnionWith(_typeFactories.Keys);
            return allTypes.Count;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 使用Expression树创建高性能的工厂委托
        /// </summary>
        /// <param name="targetType">目标类型</param>
        /// <returns>工厂委托</returns>
        private static Func<StorageTypeWrapper> CreateFactoryDelegate(Type targetType)
        {
#if !UNITY_IOS
            try
            {
                // 创建包装器类型：StorageTypeWrapper<T>
                var wrapperType = typeof(StorageTypeWrapper<>).MakeGenericType(targetType);

                // 创建包装器实例：new StorageTypeWrapper<T>()
                var newWrapper = Expression.New(wrapperType);

                // 创建变量：var wrapper = new StorageTypeWrapper<T>()
                var wrapperVar = Expression.Variable(wrapperType, "wrapper");
                var assignWrapper = Expression.Assign(wrapperVar, newWrapper);

                // 创建返回表达式：return wrapper
                var returnWrapper = Expression.Convert(wrapperVar, typeof(StorageTypeWrapper));

                // 组合所有表达式
                var block = Expression.Block(
                    new[] { wrapperVar },
                    assignWrapper,
                    returnWrapper
                );

                // 编译为委托
                var lambda = Expression.Lambda<Func<StorageTypeWrapper>>(block);
                return lambda.Compile();
            }
            catch (Exception ex)
            {
                NLogger.LogWarning("Failed to create expression delegate for type '{0}', falling back to reflection: {1}",
                    arg0: targetType.Name, arg1: ex.Message);
#endif

                // 回退到反射方式
                return () => CreateWrapperUsingReflection(targetType);

#if !UNITY_IOS
            }
#endif
        }

        /// <summary>
        /// 动态创建包装器（用于未注册的类型）
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>类型包装器</returns>
        private static StorageTypeWrapper<T> CreateDynamicWrapper<T>()
        {
            var type = typeof(T);
            lock (_lock)
            {
                _typeFactories[type] = () => new StorageTypeWrapper<T>();
            }
            return new StorageTypeWrapper<T>();
        }

        #endregion
    }
}
