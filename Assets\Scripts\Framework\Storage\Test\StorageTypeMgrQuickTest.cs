using System;
using UnityEngine;
using DGame.Framework;

namespace Storage.Test
{
    /// <summary>
    /// StorageTypeMgr快速验证测试
    /// </summary>
    public static class StorageTypeMgrQuickTest
    {
        /// <summary>
        /// 运行快速验证测试
        /// </summary>
        public static void RunQuickTest()
        {
            NLogger.Log("=== StorageTypeMgr 快速验证测试 ===");

            try
            {
                // 测试初始化
                StorageTypeMgr.Initialize();
                NLogger.Log("✓ 初始化成功，已注册类型数量: {0}", arg0: StorageTypeMgr.GetRegisteredTypeCount());

                // 测试基础类型
                TestBasicTypes();

                // 测试Unity类型
                TestUnityTypes();

                // 测试自定义类型注册
                TestCustomTypeRegistration();

                // 测试未注册类型（Expression委托）
                TestUnregisteredTypes();

                NLogger.Log("✓ 所有测试通过！");
            }
            catch (Exception ex)
            {
                NLogger.LogError("✗ 测试失败: {0}", arg0: ex.Message);
                NLogger.LogError("堆栈跟踪: {0}", arg0: ex.StackTrace);
            }

            NLogger.Log("=== 快速验证测试完成 ===");
        }

        private static void TestBasicTypes()
        {
            NLogger.Log("--- 测试基础类型 ---");

            // 测试int
            var intWrapper = StorageTypeMgr.CreateWrapperForType(typeof(int), 42);
            if (intWrapper != null && intWrapper.TryGetValue(out int intValue) && intValue == 42)
            {
                NLogger.Log("✓ int类型测试通过: {0}", arg0: intValue);
            }
            else
            {
                throw new Exception("int类型测试失败");
            }

            // 测试string
            var stringWrapper = StorageTypeMgr.CreateWrapperForType(typeof(string), "Hello World");
            if (stringWrapper != null && stringWrapper.TryGetValue(out string stringValue) && stringValue == "Hello World")
            {
                NLogger.Log("✓ string类型测试通过: {0}", arg0: stringValue);
            }
            else
            {
                throw new Exception("string类型测试失败");
            }

            // 测试bool
            var boolWrapper = StorageTypeMgr.CreateWrapperForType(typeof(bool), true);
            if (boolWrapper != null && boolWrapper.TryGetValue(out bool boolValue) && boolValue == true)
            {
                NLogger.Log("✓ bool类型测试通过: {0}", arg0: boolValue);
            }
            else
            {
                throw new Exception("bool类型测试失败");
            }
        }

        private static void TestUnityTypes()
        {
            NLogger.Log("--- 测试Unity类型 ---");

            // 测试Vector3
            var testVector = new Vector3(1.5f, 2.5f, 3.5f);
            var vectorWrapper = StorageTypeMgr.CreateWrapperForType(typeof(Vector3), testVector);
            if (vectorWrapper != null && vectorWrapper.TryGetValue(out Vector3 vectorValue) && vectorValue == testVector)
            {
                NLogger.Log("✓ Vector3类型测试通过: {0}", arg0: vectorValue);
            }
            else
            {
                throw new Exception("Vector3类型测试失败");
            }

            // 测试Quaternion
            var testQuaternion = Quaternion.Euler(45, 90, 135);
            var quaternionWrapper = StorageTypeMgr.CreateWrapperForType(typeof(Quaternion), testQuaternion);
            if (quaternionWrapper != null && quaternionWrapper.TryGetValue(out Quaternion quaternionValue))
            {
                NLogger.Log("✓ Quaternion类型测试通过: {0}", arg0: quaternionValue);
            }
            else
            {
                throw new Exception("Quaternion类型测试失败");
            }
        }

        private static void TestCustomTypeRegistration()
        {
            NLogger.Log("--- 测试自定义类型注册 ---");

            // 注册TimeSpan类型
            StorageTypeMgr.RegisterType<TimeSpan>();

            if (StorageTypeMgr.IsTypeRegistered<TimeSpan>())
            {
                NLogger.Log("✓ TimeSpan类型注册成功");
            }
            else
            {
                throw new Exception("TimeSpan类型注册失败");
            }

            // 测试创建TimeSpan包装器
            var testTimeSpan = TimeSpan.FromMinutes(30);
            var timeSpanWrapper = StorageTypeMgr.CreateWrapperForType(typeof(TimeSpan), testTimeSpan);
            if (timeSpanWrapper != null && timeSpanWrapper.TryGetValue(out TimeSpan timeSpanValue) && timeSpanValue == testTimeSpan)
            {
                NLogger.Log("✓ TimeSpan包装器创建成功: {0}", arg0: timeSpanValue);
            }
            else
            {
                throw new Exception("TimeSpan包装器创建失败");
            }

            // 注册自定义工厂函数
            StorageTypeMgr.RegisterType<DateTime>();

            var testDateTime = DateTime.Now;
            var dateTimeWrapper = StorageTypeMgr.CreateWrapperForType(typeof(DateTime), testDateTime);
            if (dateTimeWrapper != null && dateTimeWrapper.TryGetValue(out DateTime dateTimeValue))
            {
                NLogger.Log("✓ 自定义工厂函数测试通过: {0}", arg0: dateTimeValue);
            }
            else
            {
                throw new Exception("自定义工厂函数测试失败");
            }
        }

        private static void TestUnregisteredTypes()
        {
            NLogger.Log("--- 测试未注册类型（Expression委托） ---");

            // 测试Guid类型（应该使用Expression委托）
            var testGuid = Guid.NewGuid();
            var guidWrapper = StorageTypeMgr.CreateWrapperForType(typeof(Guid), testGuid);
            if (guidWrapper != null && guidWrapper.TryGetValue(out Guid guidValue) && guidValue == testGuid)
            {
                NLogger.Log("✓ Guid类型（Expression委托）测试通过: {0}", arg0: guidValue);
            }
            else
            {
                throw new Exception("Guid类型（Expression委托）测试失败");
            }

            // 测试复杂类型
            var testUri = new Uri("https://example.com");
            var uriWrapper = StorageTypeMgr.CreateWrapperForType(typeof(Uri), testUri);
            if (uriWrapper != null && uriWrapper.TryGetValue(out Uri uriValue) && uriValue.ToString() == testUri.ToString())
            {
                NLogger.Log("✓ Uri类型（Expression委托）测试通过: {0}", arg0: uriValue);
            }
            else
            {
                throw new Exception("Uri类型（Expression委托）测试失败");
            }
        }
    }
}
