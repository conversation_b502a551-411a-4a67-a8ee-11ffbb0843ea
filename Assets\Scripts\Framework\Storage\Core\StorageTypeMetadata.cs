using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using DGame.Framework;

namespace Storage
{
    /// <summary>
    /// 存储类型元数据管理器
    /// 负责将类型信息内嵌到值中的新格式序列化/反序列化
    /// 格式示例：{"Player": {"Name": {"s": "<PERSON>"}, "Age": {"i": 25}}}
    /// </summary>
    public static class StorageTypeMetadata
    {
        #region 类型标识符映射

        // 类型到标识符的映射（使用最短的标识符以压缩文件大小）
        private static readonly Dictionary<Type, string> _typeToIdentifier = new Dictionary<Type, string>
        {
            // 基础类型（单字符标识符）
            { typeof(int), "i" },
            { typeof(float), "f" },
            { typeof(double), "d" },
            { typeof(bool), "b" },
            { typeof(string), "s" },
            { typeof(long), "l" },
            { typeof(char), "c" },
            { typeof(byte), "y" },
            { typeof(short), "h" },
            { typeof(uint), "u" },
            { typeof(ulong), "v" },
            { typeof(ushort), "w" },
            { typeof(decimal), "m" },

            // Unity常用类型
            { typeof(Vector2), "V2" },
            { typeof(Vector3), "V3" },
            { typeof(Vector4), "V4" },
            { typeof(Quaternion), "Q" },
            { typeof(Color), "C" },
            { typeof(Color32), "C32" },
            { typeof(Rect), "R" },
            { typeof(Bounds), "B" },
            { typeof(Matrix4x4), "M" },

            // 数组类型
            { typeof(int[]), "i[]" },
            { typeof(float[]), "f[]" },
            { typeof(string[]), "s[]" },
            { typeof(bool[]), "b[]" },
            { typeof(Vector2[]), "V2[]" },
            { typeof(Vector3[]), "V3[]" },

            // 常用集合类型
            { typeof(List<int>), "Li" },
            { typeof(List<float>), "Lf" },
            { typeof(List<string>), "Ls" },
            { typeof(List<bool>), "Lb" },
            { typeof(List<Vector2>), "LV2" },
            { typeof(List<Vector3>), "LV3" },
            { typeof(Dictionary<string, int>), "Dsi" },
            { typeof(Dictionary<string, float>), "Dsf" },
            { typeof(Dictionary<string, string>), "Dss" },
            { typeof(Dictionary<string, bool>), "Dsb" }
        };

        // 标识符到类型的映射
        private static readonly Dictionary<string, Type> _identifierToType;

        #endregion

        #region 静态构造函数

        /// <summary>
        /// 静态构造函数，初始化反向映射
        /// </summary>
        static StorageTypeMetadata()
        {
            _identifierToType = new Dictionary<string, Type>();
            foreach (var kvp in _typeToIdentifier)
            {
                _identifierToType[kvp.Value] = kvp.Key;
            }
        }

        #endregion

        #region 内嵌格式序列化方法

        /// <summary>
        /// 将StorageTypeWrapper字典序列化为内嵌类型格式
        /// </summary>
        /// <param name="cache">类型包装器缓存</param>
        /// <returns>内嵌类型格式的数据字典</returns>
        public static Dictionary<string, object> SaveWithInlineMetadata(Dictionary<string, StorageTypeWrapper> cache)
        {
            if (cache == null || cache.Count == 0)
            {
                return new Dictionary<string, object>();
            }

            var result = new Dictionary<string, object>();

            foreach (var kvp in cache)
            {
                var wrapper = kvp.Value;
                var inlineValue = ConvertWrapperToInlineFormat(wrapper);
                if (inlineValue != null)
                {
                    result[kvp.Key] = inlineValue;
                }
                else
                {
                    NLogger.LogWarning("Failed to convert wrapper for key '{0}' to inline format", arg0: kvp.Key);
                }
            }

            NLogger.Log("Generated inline metadata for {0} items", arg0: result.Count);
            return result;
        }

        /// <summary>
        /// 从内嵌类型格式反序列化为StorageTypeWrapper字典
        /// </summary>
        /// <param name="serializedData">内嵌类型格式的序列化数据</param>
        /// <returns>恢复的类型包装器字典</returns>
        public static Dictionary<string, StorageTypeWrapper> LoadWithInlineMetadata(Dictionary<string, object> serializedData)
        {
            var result = new Dictionary<string, StorageTypeWrapper>();

            if (serializedData == null || serializedData.Count == 0)
            {
                return result;
            }

            foreach (var kvp in serializedData)
            {
                try
                {
                    var wrapper = ConvertInlineFormatToWrapper(kvp.Value);
                    if (wrapper != null)
                    {
                        result[kvp.Key] = wrapper;
                        NLogger.Log("Loaded key '{0}' with type '{1}' from inline format",
                            arg0: kvp.Key, arg1: wrapper.GetValueType().Name);
                    }
                    else
                    {
                        NLogger.LogWarning("Failed to convert inline value for key '{0}'", arg0: kvp.Key);
                    }
                }
                catch (Exception ex)
                {
                    NLogger.LogError("Error processing key '{0}': {1}", arg0: kvp.Key, arg1: ex.Message);
                }
            }

            NLogger.Log("Loaded {0} items from inline metadata format", arg0: result.Count);
            return result;
        }

        #endregion

        #region 转换方法

        /// <summary>
        /// 将StorageTypeWrapper转换为内嵌格式
        /// </summary>
        /// <param name="wrapper">类型包装器</param>
        /// <returns>内嵌格式的对象，格式为 {"类型标识符": 值}</returns>
        private static Dictionary<string, object> ConvertWrapperToInlineFormat(StorageTypeWrapper wrapper)
        {
            if (wrapper == null)
            {
                return null;
            }

            var type = wrapper.GetValueType();
            var typeIdentifier = GetTypeIdentifier(type);
            var value = wrapper.GetBoxedValue();

            // 处理特殊类型的值
            var processedValue = ProcessValueForSerialization(value, type);

            return new Dictionary<string, object>
            {
                [typeIdentifier] = processedValue
            };
        }

        /// <summary>
        /// 将内嵌格式转换为StorageTypeWrapper
        /// </summary>
        /// <param name="inlineValue">内嵌格式的值</param>
        /// <returns>StorageTypeWrapper实例</returns>
        private static StorageTypeWrapper ConvertInlineFormatToWrapper(object inlineValue)
        {
            if (inlineValue is Dictionary<string, object> inlineDict && inlineDict.Count == 1)
            {
                var kvp = inlineDict.First();
                var typeIdentifier = kvp.Key;
                var value = kvp.Value;

                var targetType = GetTypeFromIdentifier(typeIdentifier);
                if (targetType == null)
                {
                    NLogger.LogWarning("Unknown type identifier: {0}", arg0: typeIdentifier);
                    return null;
                }

                // 处理特殊类型的值
                var processedValue = ProcessValueForDeserialization(value, targetType);

                // 创建包装器
                return StorageTypeMgr.CreateWrapperForType(targetType, processedValue);
            }

            NLogger.LogWarning("Invalid inline format: expected dictionary with single key-value pair");
            return null;
        }

        /// <summary>
        /// 为序列化处理值（处理复杂类型）
        /// </summary>
        /// <param name="value">原始值</param>
        /// <param name="type">值类型</param>
        /// <returns>处理后的值</returns>
        private static object ProcessValueForSerialization(object value, Type type)
        {
            if (value == null)
            {
                return null;
            }

            // 对于数组和集合，递归处理每个元素
            if (type.IsArray)
            {
                return ProcessArrayForSerialization(value as Array);
            }

            if (IsGenericList(type))
            {
                return ProcessListForSerialization(value, type);
            }

            if (IsGenericDictionary(type))
            {
                return ProcessDictionaryForSerialization(value, type);
            }

            // 对于基础类型和Unity类型，直接返回
            return value;
        }

        /// <summary>
        /// 为反序列化处理值（处理复杂类型）
        /// </summary>
        /// <param name="value">序列化的值</param>
        /// <param name="targetType">目标类型</param>
        /// <returns>处理后的值</returns>
        private static object ProcessValueForDeserialization(object value, Type targetType)
        {
            if (value == null)
            {
                return null;
            }

            // 对于数组和集合，需要转换回正确的类型
            if (targetType.IsArray)
            {
                return ProcessArrayForDeserialization(value, targetType);
            }

            if (IsGenericList(targetType))
            {
                return ProcessListForDeserialization(value, targetType);
            }

            if (IsGenericDictionary(targetType))
            {
                return ProcessDictionaryForDeserialization(value, targetType);
            }

            // 对于基础类型，可能需要类型转换
            try
            {
                if (targetType.IsPrimitive || targetType == typeof(string) || targetType == typeof(decimal))
                {
                    return Convert.ChangeType(value, targetType);
                }

                // 对于复杂类型（如Unity类型），尝试JSON转换
                if (value is Newtonsoft.Json.Linq.JObject jobj)
                {
                    return jobj.ToObject(targetType);
                }

                return value;
            }
            catch (Exception ex)
            {
                NLogger.LogWarning("Failed to convert value for type {0}: {1}", arg0: targetType.Name, arg1: ex.Message);
                return targetType.IsValueType ? Activator.CreateInstance(targetType) : null;
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 获取类型的标识符
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>类型标识符</returns>
        public static string GetTypeIdentifier(Type type)
        {
            if (_typeToIdentifier.TryGetValue(type, out string identifier))
            {
                return identifier;
            }

            // 对于未映射的类型，使用完整类型名
            return $"T:{type.FullName}";
        }

        /// <summary>
        /// 从标识符获取类型
        /// </summary>
        /// <param name="identifier">类型标识符</param>
        /// <returns>类型，如果无法解析则返回null</returns>
        public static Type GetTypeFromIdentifier(string identifier)
        {
            if (_identifierToType.TryGetValue(identifier, out Type type))
            {
                return type;
            }

            // 处理完整类型名格式
            if (identifier.StartsWith("T:"))
            {
                string typeName = identifier.Substring(2);
                try
                {
                    return Type.GetType(typeName);
                }
                catch (Exception ex)
                {
                    NLogger.LogError("Failed to resolve type '{0}': {1}", arg0: typeName, arg1: ex.Message);
                    return null;
                }
            }

            return null;
        }

        /// <summary>
        /// 检查是否为泛型List类型
        /// </summary>
        private static bool IsGenericList(Type type)
        {
            return type.IsGenericType && type.GetGenericTypeDefinition() == typeof(List<>);
        }

        /// <summary>
        /// 检查是否为泛型Dictionary类型
        /// </summary>
        private static bool IsGenericDictionary(Type type)
        {
            return type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Dictionary<,>);
        }

        /// <summary>
        /// 处理数组序列化
        /// </summary>
        private static object ProcessArrayForSerialization(Array array)
        {
            if (array == null) return null;

            var result = new object[array.Length];
            for (int i = 0; i < array.Length; ++i)
            {
                result[i] = array.GetValue(i);
            }
            return result;
        }

        /// <summary>
        /// 处理List序列化
        /// </summary>
        private static object ProcessListForSerialization(object list, Type listType)
        {
            if (list == null) return null;

            var countProperty = listType.GetProperty("Count");
            var indexer = listType.GetProperty("Item");

            int count = (int)countProperty.GetValue(list);
            var result = new object[count];

            for (int i = 0; i < count; ++i)
            {
                result[i] = indexer.GetValue(list, new object[] { i });
            }
            return result;
        }

        /// <summary>
        /// 处理Dictionary序列化
        /// </summary>
        private static object ProcessDictionaryForSerialization(object dict, Type dictType)
        {
            if (dict == null) return null;

            var result = new Dictionary<string, object>();
            var keysProperty = dictType.GetProperty("Keys");
            var indexer = dictType.GetProperty("Item");

            var keys = keysProperty.GetValue(dict);
            foreach (var key in (System.Collections.IEnumerable)keys)
            {
                var value = indexer.GetValue(dict, new object[] { key });
                result[key.ToString()] = value;
            }
            return result;
        }

        /// <summary>
        /// 处理数组反序列化
        /// </summary>
        private static object ProcessArrayForDeserialization(object value, Type arrayType)
        {
            if (value == null) return null;

            var elementType = arrayType.GetElementType();

            if (value is object[] objectArray)
            {
                var array = Array.CreateInstance(elementType, objectArray.Length);
                for (int i = 0; i < objectArray.Length; ++i)
                {
                    // 使用StorageTypeConverter进行优化的类型转换，避免装箱拆箱
                    object convertedValue = ConvertValueToType(objectArray[i], elementType);
                    array.SetValue(convertedValue, i);
                }
                return array;
            }

            return null;
        }

        /// <summary>
        /// 处理List反序列化
        /// </summary>
        private static object ProcessListForDeserialization(object value, Type listType)
        {
            if (value == null) return null;

            var elementType = listType.GetGenericArguments()[0];
            var list = Activator.CreateInstance(listType);
            var addMethod = listType.GetMethod("Add");

            if (value is object[] objectArray)
            {
                foreach (var item in objectArray)
                {
                    // 使用StorageTypeConverter进行优化的类型转换，避免装箱拆箱
                    object convertedValue = ConvertValueToType(item, elementType);
                    addMethod.Invoke(list, new object[] { convertedValue });
                }
            }

            return list;
        }

        /// <summary>
        /// 处理Dictionary反序列化
        /// </summary>
        private static object ProcessDictionaryForDeserialization(object value, Type dictType)
        {
            if (value == null) return null;

            var genericArgs = dictType.GetGenericArguments();
            var keyType = genericArgs[0];
            var valueType = genericArgs[1];

            var dict = Activator.CreateInstance(dictType);
            var addMethod = dictType.GetMethod("Add");

            if (value is Dictionary<string, object> objectDict)
            {
                foreach (var kvp in objectDict)
                {
                    // 使用优化的类型转换，避免装箱拆箱
                    object convertedKey = ConvertValueToType(kvp.Key, keyType);
                    object convertedValue = ConvertValueToType(kvp.Value, valueType);
                    addMethod.Invoke(dict, new object[] { convertedKey, convertedValue });
                }
            }

            return dict;
        }

        /// <summary>
        /// 优化的值类型转换方法，统一使用StorageTypeConverter
        /// </summary>
        /// <param name="value">要转换的值</param>
        /// <param name="targetType">目标类型</param>
        /// <returns>转换后的值</returns>
        private static object ConvertValueToType(object value, Type targetType)
        {
            if (value == null)
            {
                return targetType.IsValueType ? Activator.CreateInstance(targetType) : null;
            }

            Type valueType = value.GetType();
            if (valueType == targetType)
            {
                return value;
            }

            try
            {
                // 统一使用StorageTypeConverter进行类型转换
                if (TryUseStorageTypeConverter(value, valueType, targetType, out object convertedValue))
                {
                    return convertedValue;
                }

                // 回退到Convert.ChangeType（保持兼容性）
                return Convert.ChangeType(value, targetType);
            }
            catch (Exception ex)
            {
                NLogger.LogWarning("Failed to convert value from {0} to {1}: {2}. Using default value.",
                    arg0: valueType.Name, arg1: targetType.Name, arg2: ex.Message);

                return targetType.IsValueType ? Activator.CreateInstance(targetType) : null;
            }
        }

        /// <summary>
        /// 尝试使用StorageTypeConverter进行类型转换
        /// </summary>
        /// <param name="value">要转换的值</param>
        /// <param name="valueType">值类型</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="convertedValue">转换后的值</param>
        /// <returns>是否转换成功</returns>
        private static bool TryUseStorageTypeConverter(object value, Type valueType, Type targetType, out object convertedValue)
        {
            convertedValue = null;

            try
            {
                // 使用反射调用StorageTypeConverter.TryConvert
                var convertMethod = typeof(StorageTypeConverter).GetMethod("TryConvert").MakeGenericMethod(valueType, targetType);
                var parameters = new object[] { value, null };
                bool success = (bool)convertMethod.Invoke(null, parameters);

                if (success)
                {
                    convertedValue = parameters[1];
                    return true;
                }
            }
            catch
            {
                // 转换失败，返回false
            }

            return false;
        }

        #endregion
    }
}
