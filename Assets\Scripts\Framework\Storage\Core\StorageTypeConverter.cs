using System;
using System.Runtime.CompilerServices;
using UnityEngine;
using Newtonsoft.Json.Linq;
using DGame.Framework;
using Storage.Serialization;
using Newtonsoft.Json;

namespace Storage
{
    /// <summary>
    /// 存储类型转换工具类
    /// 提供各种类型之间的转换功能，保持与原StorageCache相同的转换能力
    /// 使用Unsafe.As优化来避免装箱拆箱操作
    /// </summary>
    public static class StorageTypeConverter
    {
        /// <summary>
        /// 检查是否可以从源类型转换到目标类型
        /// </summary>
        /// <param name="fromType">源类型</param>
        /// <param name="toType">目标类型</param>
        /// <returns>是否可以转换</returns>
        public static bool CanConvert(Type fromType, Type toType)
        {
            if (fromType == toType)
            {
                return true;
            }

            // 处理可空类型
            var underlyingToType = Nullable.GetUnderlyingType(toType);
            if (underlyingToType != null)
            {
                toType = underlyingToType;
            }

            // 数值类型之间可以转换
            if (IsNumericType(fromType) && IsNumericType(toType))
            {
                return true;
            }

            // 枚举类型转换
            if (toType.IsEnum && (fromType == typeof(string) || IsNumericType(fromType)))
            {
                return true;
            }

            // 字符串转换
            if (toType == typeof(string))
            {
                return true;
            }

            // BigInteger转换
            if (fromType.Name == "BigInteger" && IsNumericType(toType))
            {
                return true;
            }

            // JObject转换
            if (fromType.Name == "JObject")
            {
                return true;
            }

            // 基础类型转换
            if (toType.IsPrimitive || toType == typeof(decimal) || toType == typeof(DateTime) || toType == typeof(Guid))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// 尝试将值从一种类型转换为另一种类型
        /// </summary>
        /// <typeparam name="TFrom">源类型</typeparam>
        /// <typeparam name="TTo">目标类型</typeparam>
        /// <param name="fromValue">源值</param>
        /// <param name="toValue">转换后的值</param>
        /// <returns>是否转换成功</returns>
        public static bool TryConvert<TFrom, TTo>(TFrom fromValue, out TTo toValue)
        {
            if (fromValue == null)
            {
                toValue = default;
                return !typeof(TTo).IsValueType || Nullable.GetUnderlyingType(typeof(TTo)) != null;
            }

            var targetType = typeof(TTo);
            // 获取实际运行时类型，用于处理装箱的值
            var actualSourceType = fromValue.GetType();

            // 检查运行时类型匹配（处理装箱的情况）
            if (actualSourceType == targetType)
            {
                // 直接拆箱转换，避免额外开销
                toValue = (TTo)(object)fromValue;
                return true;
            }

            var sourceType = typeof(TFrom);
            // 检查编译时类型匹配
            if (sourceType == targetType)
            {
                // 使用Unsafe.As进行零开销类型转换，避免装箱拆箱
                toValue = Unsafe.As<TFrom, TTo>(ref fromValue);
                return true;
            }

            // 处理可空类型
            var underlyingType = Nullable.GetUnderlyingType(targetType);
            if (underlyingType != null)
            {
                targetType = underlyingType;
                // 再次检查运行时类型是否匹配可空类型的基础类型
                if (actualSourceType == targetType)
                {
                    // 直接拆箱转换，避免额外开销
                    toValue = (TTo)(object)fromValue;
                    return true;
                }
            }

            try
            {
                // 处理Unity struct类型的转换
                if (IsUnityStructType(actualSourceType) || IsUnityStructType(targetType))
                {
                    if (TryConvertUnityStruct(fromValue, actualSourceType, targetType, out toValue))
                    {
                        return true;
                    }
                }

                // 处理BigInteger到数值类型的转换（JSON反序列化大数值问题）
                if (actualSourceType.Name == "BigInteger" && IsNumericType(targetType))
                {
                    return TryConvertFromBigInteger(fromValue, targetType, out toValue);
                }

                // 处理JObject到Unity类型的转换（JSON反序列化Unity类型问题）
                if (actualSourceType.Name == "JObject" && fromValue is JObject jObject)
                {
                    return TryConvertFromJObject(jObject, out toValue);
                }

                // 处理数值类型的转换（JSON反序列化常见问题）
                if (IsNumericType(actualSourceType) && IsNumericType(targetType))
                {
                    // 使用专门的数值转换方法避免装箱
                    return TryConvertNumeric(fromValue, out toValue);
                }

                // 处理枚举类型
                if (targetType.IsEnum)
                {
                    return TryConvertToEnum(fromValue, targetType, out toValue);
                }

                // 处理字符串转换
                if (targetType == typeof(string))
                {
                    // 避免通过object的装箱转换
                    var stringValue = fromValue.ToString();
                    toValue = Unsafe.As<string, TTo>(ref stringValue);
                    return true;
                }

                // 使用Convert.ChangeType进行通用转换
                if (targetType.IsPrimitive || targetType == typeof(decimal) || targetType == typeof(DateTime) || targetType == typeof(Guid))
                {
                    toValue = (TTo)Convert.ChangeType(fromValue, targetType);
                    return true;
                }
            }
            catch (Exception ex)
            {
                NLogger.LogWarning("Type conversion failed from {0} to {1}: {2}",
                    arg0: actualSourceType.Name, arg1: targetType.Name, arg2: ex.Message);
            }

            toValue = default;
            return false;
        }

        /// <summary>
        /// 检查是否为数值类型
        /// </summary>
        /// <param name="type">要检查的类型</param>
        /// <returns>是否为数值类型</returns>
        private static bool IsNumericType(Type type)
        {
            return type == typeof(byte) || type == typeof(sbyte) ||
                type == typeof(short) || type == typeof(ushort) ||
                type == typeof(int) || type == typeof(uint) ||
                type == typeof(long) || type == typeof(ulong) ||
                type == typeof(float) || type == typeof(double) ||
                type == typeof(decimal);
        }

        /// <summary>
        /// 数值类型之间的无装箱转换
        /// </summary>
        /// <typeparam name="TFrom">源类型</typeparam>
        /// <typeparam name="TTo">目标类型</typeparam>
        /// <param name="fromValue">源值</param>
        /// <param name="toValue">转换后的值</param>
        /// <returns>是否转换成功</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private static bool TryConvertNumeric<TFrom, TTo>(TFrom fromValue, out TTo toValue)
        {
            var targetType = typeof(TTo);
            try
            {
                // 根据目标类型进行优化的转换
                if (targetType == typeof(int))
                {
                    var intValue = Convert.ToInt32(fromValue);
                    toValue = Unsafe.As<int, TTo>(ref intValue);
                    return true;
                }
                if (targetType == typeof(float))
                {
                    var floatValue = Convert.ToSingle(fromValue);
                    toValue = Unsafe.As<float, TTo>(ref floatValue);
                    return true;
                }
                if (targetType == typeof(double))
                {
                    var doubleValue = Convert.ToDouble(fromValue);
                    toValue = Unsafe.As<double, TTo>(ref doubleValue);
                    return true;
                }
                if (targetType == typeof(long))
                {
                    var longValue = Convert.ToInt64(fromValue);
                    toValue = Unsafe.As<long, TTo>(ref longValue);
                    return true;
                }
                if (targetType == typeof(short))
                {
                    var shortValue = Convert.ToInt16(fromValue);
                    toValue = Unsafe.As<short, TTo>(ref shortValue);
                    return true;
                }
                if (targetType == typeof(byte))
                {
                    var byteValue = Convert.ToByte(fromValue);
                    toValue = Unsafe.As<byte, TTo>(ref byteValue);
                    return true;
                }
                if (targetType == typeof(uint))
                {
                    var uintValue = Convert.ToUInt32(fromValue);
                    toValue = Unsafe.As<uint, TTo>(ref uintValue);
                    return true;
                }
                if (targetType == typeof(ulong))
                {
                    var ulongValue = Convert.ToUInt64(fromValue);
                    toValue = Unsafe.As<ulong, TTo>(ref ulongValue);
                    return true;
                }
                if (targetType == typeof(ushort))
                {
                    var ushortValue = Convert.ToUInt16(fromValue);
                    toValue = Unsafe.As<ushort, TTo>(ref ushortValue);
                    return true;
                }
                if (targetType == typeof(decimal))
                {
                    var decimalValue = Convert.ToDecimal(fromValue);
                    toValue = Unsafe.As<decimal, TTo>(ref decimalValue);
                    return true;
                }

                // 通用转换（fallback）
                var convertedValue = Convert.ChangeType(fromValue, targetType);
                toValue = (TTo)convertedValue;
                return true;
            }
            catch
            {
                NLogger.LogError("Cannot convert value of type {0} to {1}", arg0: typeof(TFrom).Name, arg1: targetType.Name);
            }

            toValue = default;
            return false;
        }

        /// <summary>
        /// 尝试从BigInteger转换到数值类型
        /// </summary>
        private static bool TryConvertFromBigInteger<TTo>(object bigIntValue, Type targetType, out TTo toValue)
        {
            var bigIntString = bigIntValue.ToString();
            try
            {
                // 获取BigInteger的值并转换
                if (targetType == typeof(ulong))
                {
                    if (ulong.TryParse(bigIntString, out ulong ulongResult))
                    {
                        // 使用Unsafe.As避免装箱拆箱
                        toValue = Unsafe.As<ulong, TTo>(ref ulongResult);
                        return true;
                    }
                }
                else if (targetType == typeof(long))
                {
                    if (long.TryParse(bigIntString, out long longResult))
                    {
                        // 使用Unsafe.As避免装箱拆箱
                        toValue = Unsafe.As<long, TTo>(ref longResult);
                        return true;
                    }
                }
                else if (targetType == typeof(int))
                {
                    if (int.TryParse(bigIntString, out int intResult))
                    {
                        toValue = Unsafe.As<int, TTo>(ref intResult);
                        return true;
                    }
                }
                else if (targetType == typeof(float))
                {
                    if (float.TryParse(bigIntString, out float floatResult))
                    {
                        toValue = Unsafe.As<float, TTo>(ref floatResult);
                        return true;
                    }
                }
                else if (targetType == typeof(double))
                {
                    if (double.TryParse(bigIntString, out double doubleResult))
                    {
                        toValue = Unsafe.As<double, TTo>(ref doubleResult);
                        return true;
                    }
                }

                // 其他数值类型通过Convert.ChangeType处理
                toValue = (TTo)Convert.ChangeType(Convert.ToDecimal(bigIntString), targetType);
                return true;
            }
            catch
            {
                NLogger.LogError("Cannot convert BigInteger to {0}, bigIntString: {1}", arg0: targetType.Name, arg1: bigIntString);
            }

            toValue = default;
            return false;
        }

        /// <summary>
        /// 尝试从JObject转换到目标类型
        /// </summary>
        private static bool TryConvertFromJObject<TTo>(JObject jObject, out TTo toValue)
        {
            string json = jObject.ToString();
            try
            {
                // 使用StorageSerializer重新序列化和反序列化
                var convertedValue = StorageSerializer.DeserializeObject<TTo>(json);
                if (convertedValue != null)
                {
                    toValue = convertedValue;
                    return true;
                }
            }
            catch
            {
                // 转换失败，返回false
                NLogger.LogError("Cannot convert JObject to {0}, json: {1}", arg0: typeof(TTo).Name, arg1: json);
            }

            toValue = default;
            return false;
        }

        /// <summary>
        /// 尝试转换到枚举类型
        /// </summary>
        private static bool TryConvertToEnum<TTo>(object value, Type enumType, out TTo toValue)
        {
            var sourceType = value.GetType();
            try
            {
                if (sourceType == typeof(string))
                {
                    toValue = (TTo)Enum.Parse(enumType, (string)value);
                    return true;
                }
                else if (IsNumericType(sourceType))
                {
                    toValue = (TTo)Enum.ToObject(enumType, value);
                    return true;
                }
            }
            catch
            {
                // 转换失败，返回false
                NLogger.LogError("Cannot convert value of type {0} to {1}", arg0: sourceType.Name, arg1: enumType.Name);
            }

            toValue = default;
            return false;
        }

        /// <summary>
        /// 检查是否为Unity的struct类型
        /// </summary>
        /// <param name="type">要检查的类型</param>
        /// <returns>是否为Unity struct类型</returns>
        private static bool IsUnityStructType(Type type)
        {
            return type == typeof(Vector2) || type == typeof(Vector3) || type == typeof(Vector4) ||
                    type == typeof(Quaternion) || type == typeof(Color) || type == typeof(Color32) ||
                    type == typeof(Rect) || type == typeof(Bounds) || type == typeof(Matrix4x4) ||
                    type == typeof(Vector2Int) || type == typeof(Vector3Int) ||
                    type == typeof(RectInt) || type == typeof(BoundsInt);
        }

        /// <summary>
        /// 尝试转换Unity struct类型
        /// </summary>
        /// <typeparam name="TTo">目标类型</typeparam>
        /// <param name="fromValue">源值</param>
        /// <param name="actualSourceType">实际源类型</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="toValue">转换后的值</param>
        /// <returns>是否转换成功</returns>
        private static bool TryConvertUnityStruct<TTo>(object fromValue, Type actualSourceType, Type targetType, out TTo toValue)
        {
            try
            {
                // 如果源类型和目标类型都是Unity struct类型
                if (IsUnityStructType(actualSourceType) && IsUnityStructType(targetType))
                {
                    // 如果类型完全匹配，直接转换
                    if (actualSourceType == targetType)
                    {
                        toValue = (TTo)fromValue;
                        return true;
                    }

                    // 处理Vector类型之间的转换
                    if (TryConvertBetweenVectorTypes(fromValue, actualSourceType, targetType, out toValue))
                    {
                        return true;
                    }

                    // 处理Color类型之间的转换
                    if (TryConvertBetweenColorTypes(fromValue, actualSourceType, targetType, out toValue))
                    {
                        return true;
                    }
                }

                // 如果源类型是Unity struct，目标类型是其他类型（如字符串）
                if (IsUnityStructType(actualSourceType))
                {
                    if (targetType == typeof(string))
                    {
                        var stringValue = fromValue.ToString();
                        toValue = Unsafe.As<string, TTo>(ref stringValue);
                        return true;
                    }
                }

                // 如果目标类型是Unity struct，源类型是字符串（JSON反序列化场景）
                if (IsUnityStructType(targetType) && actualSourceType == typeof(string))
                {
                    return TryParseUnityStructFromString((string)fromValue, targetType, out toValue);
                }
            }
            catch (Exception ex)
            {
                NLogger.LogWarning("Unity struct conversion failed from {0} to {1}: {2}",
                    arg0: actualSourceType.Name, arg1: targetType.Name, arg2: ex.Message);
            }

            toValue = default;
            return false;
        }

        /// <summary>
        /// 尝试在Vector类型之间进行转换
        /// </summary>
        private static bool TryConvertBetweenVectorTypes<TTo>(object fromValue, Type sourceType, Type targetType, out TTo toValue)
        {
            try
            {
                // Vector2 转换
                if (sourceType == typeof(Vector2))
                {
                    var vec2 = (Vector2)fromValue;
                    if (targetType == typeof(Vector3))
                    {
                        var result = new Vector3(vec2.x, vec2.y, 0f);
                        toValue = Unsafe.As<Vector3, TTo>(ref result);
                        return true;
                    }
                    if (targetType == typeof(Vector4))
                    {
                        var result = new Vector4(vec2.x, vec2.y, 0f, 0f);
                        toValue = Unsafe.As<Vector4, TTo>(ref result);
                        return true;
                    }
                    if (targetType == typeof(Vector2Int))
                    {
                        var result = new Vector2Int(Mathf.RoundToInt(vec2.x), Mathf.RoundToInt(vec2.y));
                        toValue = Unsafe.As<Vector2Int, TTo>(ref result);
                        return true;
                    }
                }

                // Vector3 转换
                if (sourceType == typeof(Vector3))
                {
                    var vec3 = (Vector3)fromValue;
                    if (targetType == typeof(Vector2))
                    {
                        var result = new Vector2(vec3.x, vec3.y);
                        toValue = Unsafe.As<Vector2, TTo>(ref result);
                        return true;
                    }
                    if (targetType == typeof(Vector4))
                    {
                        var result = new Vector4(vec3.x, vec3.y, vec3.z, 0f);
                        toValue = Unsafe.As<Vector4, TTo>(ref result);
                        return true;
                    }
                    if (targetType == typeof(Vector3Int))
                    {
                        var result = new Vector3Int(Mathf.RoundToInt(vec3.x), Mathf.RoundToInt(vec3.y), Mathf.RoundToInt(vec3.z));
                        toValue = Unsafe.As<Vector3Int, TTo>(ref result);
                        return true;
                    }
                }

                // Vector4 转换
                if (sourceType == typeof(Vector4))
                {
                    var vec4 = (Vector4)fromValue;
                    if (targetType == typeof(Vector2))
                    {
                        var result = new Vector2(vec4.x, vec4.y);
                        toValue = Unsafe.As<Vector2, TTo>(ref result);
                        return true;
                    }
                    if (targetType == typeof(Vector3))
                    {
                        var result = new Vector3(vec4.x, vec4.y, vec4.z);
                        toValue = Unsafe.As<Vector3, TTo>(ref result);
                        return true;
                    }
                    if (targetType == typeof(Quaternion))
                    {
                        var result = new Quaternion(vec4.x, vec4.y, vec4.z, vec4.w);
                        toValue = Unsafe.As<Quaternion, TTo>(ref result);
                        return true;
                    }
                    if (targetType == typeof(Color))
                    {
                        var result = new Color(vec4.x, vec4.y, vec4.z, vec4.w);
                        toValue = Unsafe.As<Color, TTo>(ref result);
                        return true;
                    }
                }

                // Quaternion 转换
                if (sourceType == typeof(Quaternion))
                {
                    var quat = (Quaternion)fromValue;
                    if (targetType == typeof(Vector4))
                    {
                        var result = new Vector4(quat.x, quat.y, quat.z, quat.w);
                        toValue = Unsafe.As<Vector4, TTo>(ref result);
                        return true;
                    }
                }

                // 整数Vector转换
                if (sourceType == typeof(Vector2Int))
                {
                    var vec2Int = (Vector2Int)fromValue;
                    if (targetType == typeof(Vector2))
                    {
                        var result = new Vector2(vec2Int.x, vec2Int.y);
                        toValue = Unsafe.As<Vector2, TTo>(ref result);
                        return true;
                    }
                    if (targetType == typeof(Vector3Int))
                    {
                        var result = new Vector3Int(vec2Int.x, vec2Int.y, 0);
                        toValue = Unsafe.As<Vector3Int, TTo>(ref result);
                        return true;
                    }
                }

                if (sourceType == typeof(Vector3Int))
                {
                    var vec3Int = (Vector3Int)fromValue;
                    if (targetType == typeof(Vector3))
                    {
                        var result = new Vector3(vec3Int.x, vec3Int.y, vec3Int.z);
                        toValue = Unsafe.As<Vector3, TTo>(ref result);
                        return true;
                    }
                    if (targetType == typeof(Vector2Int))
                    {
                        var result = new Vector2Int(vec3Int.x, vec3Int.y);
                        toValue = Unsafe.As<Vector2Int, TTo>(ref result);
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                NLogger.LogWarning("Vector type conversion failed: {0}", arg0: ex.Message);
            }

            toValue = default;
            return false;
        }

        /// <summary>
        /// 尝试在Color类型之间进行转换
        /// </summary>
        private static bool TryConvertBetweenColorTypes<TTo>(object fromValue, Type sourceType, Type targetType, out TTo toValue)
        {
            try
            {
                // Color 转换
                if (sourceType == typeof(Color))
                {
                    var color = (Color)fromValue;
                    if (targetType == typeof(Color32))
                    {
                        var result = (Color32)color;
                        toValue = Unsafe.As<Color32, TTo>(ref result);
                        return true;
                    }
                    if (targetType == typeof(Vector4))
                    {
                        var result = new Vector4(color.r, color.g, color.b, color.a);
                        toValue = Unsafe.As<Vector4, TTo>(ref result);
                        return true;
                    }
                }

                // Color32 转换
                if (sourceType == typeof(Color32))
                {
                    var color32 = (Color32)fromValue;
                    if (targetType == typeof(Color))
                    {
                        var result = (Color)color32;
                        toValue = Unsafe.As<Color, TTo>(ref result);
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                NLogger.LogWarning("Color type conversion failed: {0}", arg0: ex.Message);
            }

            toValue = default;
            return false;
        }

        /// <summary>
        /// 尝试从字符串解析Unity struct类型
        /// </summary>
        private static bool TryParseUnityStructFromString<TTo>(string stringValue, Type targetType, out TTo toValue)
        {
            try
            {
                // 这里可以使用JSON反序列化或自定义解析逻辑
                // 对于简单情况，可以尝试JSON反序列化
                if (!string.IsNullOrEmpty(stringValue) && stringValue.StartsWith("{") && stringValue.EndsWith("}"))
                {
                    // 使用JsonConvert进行反序列化，因为StorageSerializer没有非泛型版本
                    var parsedValue = JsonConvert.DeserializeObject(stringValue, targetType);
                    if (parsedValue != null)
                    {
                        toValue = (TTo)parsedValue;
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                NLogger.LogWarning("Failed to parse Unity struct from string: {0}", arg0: ex.Message);
            }

            toValue = default;
            return false;
        }
    }
}
