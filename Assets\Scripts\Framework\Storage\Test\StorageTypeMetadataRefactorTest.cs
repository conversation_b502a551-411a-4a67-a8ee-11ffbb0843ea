using System;
using System.Collections.Generic;
using UnityEngine;
using DGame.Framework;

namespace Storage.Test
{
    /// <summary>
    /// StorageTypeMetadata重构验证测试
    /// 验证重构后的类型转换功能是否正常工作
    /// </summary>
    public static class StorageTypeMetadataRefactorTest
    {
        /// <summary>
        /// 运行重构验证测试
        /// </summary>
        public static void RunRefactorTest()
        {
            NLogger.Log("=== StorageTypeMetadata 重构验证测试开始 ===");

            try
            {
                // 测试基础类型的内嵌格式序列化/反序列化
                TestBasicTypesInlineFormat();

                // 测试Unity类型的内嵌格式序列化/反序列化
                TestUnityTypesInlineFormat();

                // 测试集合类型的内嵌格式序列化/反序列化
                TestCollectionTypesInlineFormat();

                // 测试复杂数据的内嵌格式序列化/反序列化
                TestComplexDataInlineFormat();

                // 测试类型转换功能
                TestTypeConversionFunctionality();

                NLogger.Log("✓ 所有重构验证测试通过！");
            }
            catch (Exception ex)
            {
                NLogger.LogError("✗ 重构验证测试失败: {0}", arg0: ex.Message);
                NLogger.LogError("堆栈跟踪: {0}", arg0: ex.StackTrace);
            }

            NLogger.Log("=== StorageTypeMetadata 重构验证测试完成 ===");
        }

        private static void TestBasicTypesInlineFormat()
        {
            NLogger.Log("--- 测试基础类型内嵌格式 ---");

            var cache = new Dictionary<string, StorageTypeWrapper>();
            
            // 添加基础类型数据
            cache["intValue"] = StorageTypeMgr.CreateWrapper(42);
            cache["floatValue"] = StorageTypeMgr.CreateWrapper(3.14f);
            cache["stringValue"] = StorageTypeMgr.CreateWrapper("Hello World");
            cache["boolValue"] = StorageTypeMgr.CreateWrapper(true);

            // 序列化为内嵌格式
            var serializedData = StorageTypeMetadata.SaveWithInlineMetadata(cache);
            
            if (serializedData.Count != 4)
            {
                throw new Exception($"序列化数据数量不匹配，期望4，实际{serializedData.Count}");
            }

            // 反序列化
            var deserializedCache = StorageTypeMetadata.LoadWithInlineMetadata(serializedData);
            
            if (deserializedCache.Count != 4)
            {
                throw new Exception($"反序列化数据数量不匹配，期望4，实际{deserializedCache.Count}");
            }

            // 验证数据正确性
            if (!deserializedCache["intValue"].TryGetValue(out int intValue) || intValue != 42)
            {
                throw new Exception("int值验证失败");
            }
            
            if (!deserializedCache["floatValue"].TryGetValue(out float floatValue) || !Mathf.Approximately(floatValue, 3.14f))
            {
                throw new Exception("float值验证失败");
            }
            
            if (!deserializedCache["stringValue"].TryGetValue(out string stringValue) || stringValue != "Hello World")
            {
                throw new Exception("string值验证失败");
            }
            
            if (!deserializedCache["boolValue"].TryGetValue(out bool boolValue) || boolValue != true)
            {
                throw new Exception("bool值验证失败");
            }

            NLogger.Log("✓ 基础类型内嵌格式测试通过");
        }

        private static void TestUnityTypesInlineFormat()
        {
            NLogger.Log("--- 测试Unity类型内嵌格式 ---");

            var cache = new Dictionary<string, StorageTypeWrapper>();
            
            var testVector3 = new Vector3(1.5f, 2.5f, 3.5f);
            var testQuaternion = Quaternion.Euler(45, 90, 135);
            var testColor = new Color(0.5f, 0.7f, 0.9f, 1.0f);

            cache["vector3Value"] = StorageTypeMgr.CreateWrapper(testVector3);
            cache["quaternionValue"] = StorageTypeMgr.CreateWrapper(testQuaternion);
            cache["colorValue"] = StorageTypeMgr.CreateWrapper(testColor);

            // 序列化为内嵌格式
            var serializedData = StorageTypeMetadata.SaveWithInlineMetadata(cache);
            
            // 反序列化
            var deserializedCache = StorageTypeMetadata.LoadWithInlineMetadata(serializedData);
            
            // 验证数据正确性
            if (!deserializedCache["vector3Value"].TryGetValue(out Vector3 vector3Value) || vector3Value != testVector3)
            {
                throw new Exception("Vector3值验证失败");
            }
            
            if (!deserializedCache["quaternionValue"].TryGetValue(out Quaternion quaternionValue) || quaternionValue != testQuaternion)
            {
                throw new Exception("Quaternion值验证失败");
            }
            
            if (!deserializedCache["colorValue"].TryGetValue(out Color colorValue) || colorValue != testColor)
            {
                throw new Exception("Color值验证失败");
            }

            NLogger.Log("✓ Unity类型内嵌格式测试通过");
        }

        private static void TestCollectionTypesInlineFormat()
        {
            NLogger.Log("--- 测试集合类型内嵌格式 ---");

            var cache = new Dictionary<string, StorageTypeWrapper>();
            
            var testIntArray = new int[] { 1, 2, 3, 4, 5 };
            var testStringList = new List<string> { "Item1", "Item2", "Item3" };

            cache["intArray"] = StorageTypeMgr.CreateWrapper(testIntArray);
            cache["stringList"] = StorageTypeMgr.CreateWrapper(testStringList);

            // 序列化为内嵌格式
            var serializedData = StorageTypeMetadata.SaveWithInlineMetadata(cache);
            
            // 反序列化
            var deserializedCache = StorageTypeMetadata.LoadWithInlineMetadata(serializedData);
            
            // 验证数组
            if (!deserializedCache["intArray"].TryGetValue(out int[] intArray))
            {
                throw new Exception("int[]反序列化失败");
            }
            
            if (intArray.Length != testIntArray.Length)
            {
                throw new Exception("int[]长度不匹配");
            }
            
            for (int i = 0; i < intArray.Length; i++)
            {
                if (intArray[i] != testIntArray[i])
                {
                    throw new Exception($"int[]元素{i}不匹配");
                }
            }

            // 验证List
            if (!deserializedCache["stringList"].TryGetValue(out List<string> stringList))
            {
                throw new Exception("List<string>反序列化失败");
            }
            
            if (stringList.Count != testStringList.Count)
            {
                throw new Exception("List<string>长度不匹配");
            }
            
            for (int i = 0; i < stringList.Count; i++)
            {
                if (stringList[i] != testStringList[i])
                {
                    throw new Exception($"List<string>元素{i}不匹配");
                }
            }

            NLogger.Log("✓ 集合类型内嵌格式测试通过");
        }

        private static void TestComplexDataInlineFormat()
        {
            NLogger.Log("--- 测试复杂数据内嵌格式 ---");

            var cache = new Dictionary<string, StorageTypeWrapper>();
            
            // 添加各种类型的数据
            cache["playerId"] = StorageTypeMgr.CreateWrapper(12345);
            cache["playerName"] = StorageTypeMgr.CreateWrapper("TestPlayer");
            cache["position"] = StorageTypeMgr.CreateWrapper(new Vector3(10.5f, 20.5f, 30.5f));
            cache["isActive"] = StorageTypeMgr.CreateWrapper(true);
            cache["scores"] = StorageTypeMgr.CreateWrapper(new int[] { 100, 200, 300 });

            // 序列化为内嵌格式
            var serializedData = StorageTypeMetadata.SaveWithInlineMetadata(cache);
            
            // 反序列化
            var deserializedCache = StorageTypeMetadata.LoadWithInlineMetadata(serializedData);
            
            // 验证所有数据
            if (deserializedCache.Count != 5)
            {
                throw new Exception($"复杂数据数量不匹配，期望5，实际{deserializedCache.Count}");
            }

            // 验证每个字段
            if (!deserializedCache["playerId"].TryGetValue(out int playerId) || playerId != 12345)
            {
                throw new Exception("playerId验证失败");
            }
            
            if (!deserializedCache["playerName"].TryGetValue(out string playerName) || playerName != "TestPlayer")
            {
                throw new Exception("playerName验证失败");
            }
            
            if (!deserializedCache["position"].TryGetValue(out Vector3 position) || position != new Vector3(10.5f, 20.5f, 30.5f))
            {
                throw new Exception("position验证失败");
            }
            
            if (!deserializedCache["isActive"].TryGetValue(out bool isActive) || isActive != true)
            {
                throw new Exception("isActive验证失败");
            }
            
            if (!deserializedCache["scores"].TryGetValue(out int[] scores) || scores.Length != 3 || scores[0] != 100 || scores[1] != 200 || scores[2] != 300)
            {
                throw new Exception("scores验证失败");
            }

            NLogger.Log("✓ 复杂数据内嵌格式测试通过");
        }

        private static void TestTypeConversionFunctionality()
        {
            NLogger.Log("--- 测试类型转换功能 ---");

            // 测试类型标识符功能
            var intType = typeof(int);
            var intIdentifier = StorageTypeMetadata.GetTypeIdentifier(intType);
            var recoveredType = StorageTypeMetadata.GetTypeFromIdentifier(intIdentifier);
            
            if (recoveredType != intType)
            {
                throw new Exception("类型标识符转换失败");
            }

            // 测试Unity类型标识符
            var vector3Type = typeof(Vector3);
            var vector3Identifier = StorageTypeMetadata.GetTypeIdentifier(vector3Type);
            var recoveredVector3Type = StorageTypeMetadata.GetTypeFromIdentifier(vector3Identifier);
            
            if (recoveredVector3Type != vector3Type)
            {
                throw new Exception("Vector3类型标识符转换失败");
            }

            // 测试未知类型标识符
            var customType = typeof(DateTime);
            var customIdentifier = StorageTypeMetadata.GetTypeIdentifier(customType);
            var recoveredCustomType = StorageTypeMetadata.GetTypeFromIdentifier(customIdentifier);
            
            if (recoveredCustomType != customType)
            {
                throw new Exception("自定义类型标识符转换失败");
            }

            NLogger.Log("✓ 类型转换功能测试通过");
        }
    }
}
